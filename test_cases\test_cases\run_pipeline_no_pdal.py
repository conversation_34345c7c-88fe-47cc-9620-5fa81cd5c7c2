#!/usr/bin/env python3
"""
Simplified LiDAR Processing Pipeline (No PDAL)
Runs the pipeline using existing filtered LAS file, skipping PDAL step
"""

import os
import sys
import time
from pathlib import Path
import subprocess

def run_script(script_path, description):
    """Run a Python script and handle errors"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run([
            sys.executable, str(script_path)
        ], check=True, capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        print(f"{description} completed successfully!")
        return True

    except subprocess.CalledProcessError as e:
        print(f"{description} failed with return code {e.returncode}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False
    except Exception as e:
        print(f"Error running {description}: {str(e)}")
        return False

def check_python_dependencies():
    """Check if required Python dependencies are available"""
    print("Checking Python dependencies...")

    required_packages = [
        'laspy', 'numpy', 'pandas', 'geopandas',
        'shapely', 'sklearn', 'matplotlib'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"Missing dependencies: {', '.join(missing_packages)}")
        print(f"Install with: pip install {' '.join(missing_packages)}")
        return False

    print("All Python dependencies are available!")
    return True

def check_input_file():
    """Check if filtered LAS file exists"""
    script_dir = Path(__file__).parent
    
    # Check parent directory first
    input_file = script_dir.parent / "FusedLidar_Lidar1_UTM32N_filtered.las"
    
    if not input_file.exists():
        input_file = script_dir / "FusedLidar_Lidar1_UTM32N_filtered.las"
    
    if input_file.exists():
        file_size = input_file.stat().st_size / (1024 * 1024)  # MB
        print(f"Filtered LAS file found: {input_file} ({file_size:.2f} MB)")
        return True
    else:
        print(f"Filtered LAS file not found!")
        print("Expected locations:")
        print(f"  - {script_dir.parent / 'FusedLidar_Lidar1_UTM32N_filtered.las'}")
        print(f"  - {script_dir / 'FusedLidar_Lidar1_UTM32N_filtered.las'}")
        return False

def main():
    """Main pipeline execution"""
    start_time = time.time()
    
    print("Simplified LiDAR Processing Pipeline (No PDAL)")
    print("=" * 60)
    print("This pipeline will:")
    print("1. Use existing filtered LAS file (skipping PDAL)")
    print("2. Process data in spatial chunks")
    print("3. Extract polygons and line strings")
    print("4. Generate GeoJSON output")
    print("=" * 60)
    
    # Get script directory
    script_dir = Path(__file__).parent
    
    # Check dependencies
    if not check_python_dependencies():
        print("\nPipeline aborted due to missing dependencies!")
        sys.exit(1)

    # Check input file
    if not check_input_file():
        print("\nPipeline aborted due to missing input file!")
        sys.exit(1)
    
    # Pipeline steps (skipping PDAL)
    steps = [
        (script_dir / "chunk_processor.py", "Chunk Processing"),
        (script_dir / "geojson_generator.py", "GeoJSON Generation")
    ]
    
    # Execute pipeline steps
    for step_script, step_description in steps:
        if not step_script.exists():
            print(f"Script not found: {step_script}")
            sys.exit(1)

        success = run_script(step_script, step_description)

        if not success:
            print(f"\nPipeline failed at step: {step_description}")
            sys.exit(1)
        
        # Small delay between steps
        time.sleep(1)
    
    # Pipeline completed
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n{'='*60}")
    print("SIMPLIFIED PIPELINE FINISHED SUCCESSFULLY!")
    print(f"{'='*60}")
    print(f"Total execution time: {duration:.2f} seconds")
    print(f"Output files are in: {script_dir}")
    print("\nGenerated outputs:")
    print("  - chunks/ - Processed chunk files")
    print("  - geojson_output/ - GeoJSON feature files")
    print("    - polygons.geojson - Extracted polygons")
    print("    - lines.geojson - Extracted line strings")
    print("    - points.geojson - Sample points")
    print("    - combined_features.geojson - All features combined")

    # Check output files
    output_dir = script_dir / "geojson_output"
    if output_dir.exists():
        geojson_files = list(output_dir.glob("*.geojson"))
        print(f"\nGenerated {len(geojson_files)} GeoJSON files:")
        for file in geojson_files:
            file_size = file.stat().st_size / 1024  # KB
            print(f"   - {file.name} ({file_size:.1f} KB)")

if __name__ == "__main__":
    main()
