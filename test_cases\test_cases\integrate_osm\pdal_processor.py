#!/usr/bin/env python3
"""
PDAL Pipeline Processor
Handles LiDAR data filtering and reprojection using PDAL
"""

import subprocess
import os
import sys
import json
from pathlib import Path
import tempfile

class PDALProcessor:
    """PDAL pipeline processor for LiDAR data"""
    
    def __init__(self, input_file: str, output_file: str = None):
        """
        Initialize PDAL processor
        
        Args:
            input_file (str): Path to input LAS file
            output_file (str): Path to output LAS file (optional)
        """
        self.input_file = Path(input_file)
        
        if output_file:
            self.output_file = Path(output_file)
        else:
            # Generate output filename
            stem = self.input_file.stem
            suffix = self.input_file.suffix
            self.output_file = self.input_file.parent / f"{stem}_filtered{suffix}"
    
    def create_pipeline_config(self, 
                             intensity_min: int = 75, 
                             intensity_max: int = 150,
                             input_crs: str = "EPSG:4326",
                             output_crs: str = "EPSG:25832",
                             scale_x: float = 0.001,
                             scale_y: float = 0.001,
                             scale_z: float = 0.001,
                             offset_x: float = 0.0,
                             offset_y: float = 5000000.0,
                             offset_z: float = 0.0) -> dict:
        """
        Create PDAL pipeline configuration
        
        Args:
            intensity_min (int): Minimum intensity value
            intensity_max (int): Maximum intensity value
            input_crs (str): Input coordinate reference system
            output_crs (str): Output coordinate reference system
            scale_x (float): X scale factor
            scale_y (float): Y scale factor
            scale_z (float): Z scale factor
            offset_x (float): X offset
            offset_y (float): Y offset
            offset_z (float): Z offset
        
        Returns:
            dict: PDAL pipeline configuration
        """
        
        pipeline = {
            "pipeline": [
                {
                    "type": "readers.las",
                    "filename": str(self.input_file)
                },
                {
                    "type": "filters.range",
                    "limits": f"Intensity[{intensity_min}:{intensity_max}]"
                },
                {
                    "type": "filters.reprojection",
                    "in_srs": input_crs,
                    "out_srs": output_crs
                },
                {
                    "type": "writers.las",
                    "filename": str(self.output_file),
                    "a_srs": output_crs,
                    "scale_x": scale_x,
                    "scale_y": scale_y,
                    "scale_z": scale_z,
                    "offset_x": offset_x,
                    "offset_y": offset_y,
                    "offset_z": offset_z
                }
            ]
        }
        
        return pipeline
    
    def run_pipeline(self, pipeline_config: dict = None, verbose: bool = True) -> bool:
        """
        Run PDAL pipeline
        
        Args:
            pipeline_config (dict): PDAL pipeline configuration (optional)
            verbose (bool): Print verbose output
        
        Returns:
            bool: True if successful, False otherwise
        """
        
        # Use default configuration if none provided
        if pipeline_config is None:
            pipeline_config = self.create_pipeline_config()
        
        # Check if input file exists
        if not self.input_file.exists():
            print(f"Error: Input LAS file not found: {self.input_file}")
            return False
        
        # Create output directory if it doesn't exist
        self.output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Create temporary pipeline file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(pipeline_config, f, indent=2)
            pipeline_file = f.name
        
        try:
            if verbose:
                print(f"Running PDAL pipeline:")
                print(f"  Input: {self.input_file}")
                print(f"  Output: {self.output_file}")
                print(f"  Pipeline: {pipeline_file}")
            
            # Run PDAL command
            cmd = ['pdal', 'pipeline', pipeline_file]
            
            if verbose:
                print(f"  Executing: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            
            if verbose:
                print("PDAL pipeline completed successfully!")
                if result.stdout:
                    print("STDOUT:", result.stdout)
            
            # Verify output file was created
            if self.output_file.exists():
                file_size = self.output_file.stat().st_size / (1024 * 1024)  # MB
                if verbose:
                    print(f"Output file created: {self.output_file} ({file_size:.2f} MB)")
                return True
            else:
                print(f"Error: Output file was not created: {self.output_file}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"PDAL pipeline failed with return code {e.returncode}")
            print(f"STDERR: {e.stderr}")
            return False
        except FileNotFoundError:
            print("Error: PDAL command not found. Please install PDAL:")
            print("  conda install -c conda-forge pdal")
            print("  or visit: https://pdal.io/en/latest/download.html")
            return False
        except Exception as e:
            print(f"Error running PDAL pipeline: {str(e)}")
            return False
        finally:
            # Clean up temporary pipeline file
            try:
                os.unlink(pipeline_file)
            except:
                pass
    
    def get_info(self) -> dict:
        """
        Get information about the input LAS file using PDAL
        
        Returns:
            dict: LAS file information
        """
        try:
            cmd = ['pdal', 'info', str(self.input_file)]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            info = json.loads(result.stdout)
            return info
            
        except subprocess.CalledProcessError as e:
            print(f"Error getting LAS info: {e.stderr}")
            return {}
        except FileNotFoundError:
            print("Error: PDAL command not found")
            return {}
        except json.JSONDecodeError as e:
            print(f"Error parsing PDAL info output: {e}")
            return {}
        except Exception as e:
            print(f"Error getting LAS info: {str(e)}")
            return {}

def main():
    """Main function with command line interface"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='Process LiDAR data using PDAL',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic filtering and reprojection
  python pdal_processor.py -i input.las -o output.las
  
  # Custom intensity range
  python pdal_processor.py -i input.las -o output.las --intensity-min 50 --intensity-max 200
  
  # Different coordinate systems
  python pdal_processor.py -i input.las -o output.las --input-crs EPSG:4326 --output-crs EPSG:25834
  
  # Get file information
  python pdal_processor.py -i input.las --info-only
        """
    )
    
    parser.add_argument('--input', '-i', required=True, help='Input LAS file path')
    parser.add_argument('--output', '-o', help='Output LAS file path')
    parser.add_argument('--intensity-min', type=int, default=75, help='Minimum intensity value')
    parser.add_argument('--intensity-max', type=int, default=150, help='Maximum intensity value')
    parser.add_argument('--input-crs', default='EPSG:4326', help='Input coordinate system')
    parser.add_argument('--output-crs', default='EPSG:25832', help='Output coordinate system')
    parser.add_argument('--scale-x', type=float, default=0.001, help='X scale factor')
    parser.add_argument('--scale-y', type=float, default=0.001, help='Y scale factor')
    parser.add_argument('--scale-z', type=float, default=0.001, help='Z scale factor')
    parser.add_argument('--offset-x', type=float, default=0.0, help='X offset')
    parser.add_argument('--offset-y', type=float, default=5000000.0, help='Y offset')
    parser.add_argument('--offset-z', type=float, default=0.0, help='Z offset')
    parser.add_argument('--info-only', action='store_true', help='Only show file information')
    parser.add_argument('--quiet', '-q', action='store_true', help='Suppress verbose output')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = PDALProcessor(args.input, args.output)
    
    if args.info_only:
        # Show file information
        print(f"LAS File Information: {args.input}")
        print("=" * 50)
        
        info = processor.get_info()
        if info:
            # Extract key information
            if 'stats' in info:
                stats = info['stats']
                for stat in stats:
                    if stat['name'] in ['X', 'Y', 'Z']:
                        print(f"{stat['name']}: {stat['minimum']:.2f} to {stat['maximum']:.2f}")
                    elif stat['name'] == 'Intensity':
                        print(f"Intensity: {stat['minimum']:.0f} to {stat['maximum']:.0f}")
            
            if 'metadata' in info:
                metadata = info['metadata']
                if 'count' in metadata:
                    print(f"Point count: {metadata['count']:,}")
                if 'srs' in metadata:
                    print(f"Coordinate system: {metadata['srs']['compoundwkt']}")
        else:
            print("Failed to get file information")
    
    else:
        # Run processing pipeline
        pipeline_config = processor.create_pipeline_config(
            intensity_min=args.intensity_min,
            intensity_max=args.intensity_max,
            input_crs=args.input_crs,
            output_crs=args.output_crs,
            scale_x=args.scale_x,
            scale_y=args.scale_y,
            scale_z=args.scale_z,
            offset_x=args.offset_x,
            offset_y=args.offset_y,
            offset_z=args.offset_z
        )
        
        success = processor.run_pipeline(pipeline_config, verbose=not args.quiet)
        
        if success:
            print("Processing completed successfully!")
        else:
            print("Processing failed!")
            sys.exit(1)

if __name__ == "__main__":
    main()
