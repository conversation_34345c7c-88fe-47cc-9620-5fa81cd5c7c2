#!/usr/bin/env python3
"""
LiDAR + OpenStreetMap Integrated Pipeline
Combines LiDAR feature extraction with OSM road/lane data for comprehensive mapping
"""

import sys
import time
import argparse
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Import our modules
from complete_lidar_pipeline import LiDARProcessor
from coordinate_transformer import transform_geojson
from precise_las_to_lane_geojson import (
    convert_polygon_to_wgs84,
    get_osm_data_from_polygon,
    create_lane_geometries,
    create_geojson_output
)

import geopandas as gpd
import pandas as pd
import numpy as np
import laspy
from pyproj import CRS
from shapely.geometry import Polygon, MultiPolygon
from scipy.spatial import ConvexHull

class IntegratedLiDARPipeline:
    """Integrated pipeline combining LiDAR processing with OSM data"""
    
    def __init__(self, las_file: str, output_dir: str = "integrated_output"):
        """
        Initialize the integrated pipeline
        
        Args:
            las_file (str): Path to LAS file
            output_dir (str): Output directory
        """
        self.las_file = las_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Processing components
        self.lidar_processor = None
        self.osm_data = None
        self.boundary_polygon = None
        
    def extract_lidar_features(self, 
                             chunk_size: float = 500.0,
                             processing_crs: str = "EPSG:25832") -> bool:
        """
        Extract features from LiDAR data
        
        Args:
            chunk_size (float): Chunk size in meters
            processing_crs (str): Processing coordinate system
            
        Returns:
            bool: Success status
        """
        print("STEP 1: LiDAR Feature Extraction")
        print("-" * 40)
        
        # Create LiDAR processor
        lidar_output_dir = self.output_dir / "lidar_features"
        self.lidar_processor = LiDARProcessor(
            self.las_file,
            chunk_size_meters=chunk_size,
            output_dir=str(lidar_output_dir)
        )
        
        # Run LiDAR processing
        success = self.lidar_processor.run_complete_pipeline(output_crs=processing_crs)
        
        if success:
            print(f"LiDAR features extracted:")
            print(f"  Polygons: {len(self.lidar_processor.all_polygons)}")
            print(f"  Lines: {len(self.lidar_processor.all_lines)}")
            print(f"  Points: {len(self.lidar_processor.all_points)}")
        
        return success

    def extract_las_boundary_robust(self, sample_rate: float = 0.01) -> Polygon:
        """
        Extract boundary polygon from LAS file using robust convex hull method

        Args:
            sample_rate (float): Fraction of points to sample

        Returns:
            Polygon: Boundary polygon
        """
        print("Extracting LAS boundary using robust method...")

        # Read LAS file
        las = laspy.read(self.las_file)
        points = las.xyz

        print(f"LAS file contains {len(points):,} points")

        # Sample points
        num_points = len(points)
        sample_size = max(1000, int(num_points * sample_rate))
        sample_size = min(sample_size, num_points)

        print(f"Sampling {sample_size:,} points ({sample_rate*100:.2f}% of total)")

        # Random sampling
        indices = np.random.choice(num_points, sample_size, replace=False)
        sampled_points = points[indices]

        # Get 2D points (x, y only)
        points_2d = sampled_points[:, :2]

        # Create convex hull
        try:
            hull = ConvexHull(points_2d)
            hull_points = points_2d[hull.vertices]
            boundary = Polygon(hull_points)

            print(f"Boundary polygon created with {len(hull_points)} vertices")
            print(f"Boundary bounds: X: {boundary.bounds[0]:.2f} to {boundary.bounds[2]:.2f}, Y: {boundary.bounds[1]:.2f} to {boundary.bounds[3]:.2f}")

            return boundary

        except Exception as e:
            print(f"Error creating convex hull: {e}")
            # Fallback to simple bounding box
            min_x, min_y = points_2d.min(axis=0)
            max_x, max_y = points_2d.max(axis=0)

            boundary = Polygon([
                (min_x, min_y),
                (max_x, min_y),
                (max_x, max_y),
                (min_x, max_y),
                (min_x, min_y)
            ])

            print(f"Fallback to bounding box: {boundary.bounds}")
            return boundary

    def extract_osm_features(self,
                           utm_epsg: int = 25832,
                           sample_rate: float = 0.01,
                           alpha: float = 100.0) -> bool:
        """
        Extract OSM features for the LAS file area
        
        Args:
            utm_epsg (int): UTM EPSG code
            sample_rate (float): LAS sampling rate for boundary
            alpha (float): Alpha shape parameter
            
        Returns:
            bool: Success status
        """
        print("\nSTEP 2: OpenStreetMap Feature Extraction")
        print("-" * 40)
        
        try:
            # Extract boundary from LAS file using robust method
            self.boundary_polygon = self.extract_las_boundary_robust(sample_rate)
            
            # Convert to WGS84
            print("Converting boundary to WGS84...")
            wgs84_polygon, _ = convert_polygon_to_wgs84(
                self.boundary_polygon, utm_epsg
            )
            
            # Get OSM data
            print("Querying OpenStreetMap...")
            self.osm_data = get_osm_data_from_polygon(wgs84_polygon)
            
            if not self.osm_data.empty:
                print(f"OSM features retrieved: {len(self.osm_data)}")
                
                # Save raw OSM data
                osm_output_dir = self.output_dir / "osm_features"
                osm_output_dir.mkdir(exist_ok=True)
                
                osm_file = osm_output_dir / "raw_osm_data.geojson"
                self.osm_data.to_file(osm_file, driver="GeoJSON")
                print(f"Raw OSM data saved: {osm_file}")
                
                return True
            else:
                print("Warning: No OSM data found in the area")
                return False
                
        except Exception as e:
            print(f"Error extracting OSM features: {str(e)}")
            return False
    
    def create_lane_geometries(self, utm_epsg: int = 25832) -> bool:
        """
        Create detailed lane geometries from OSM data
        
        Args:
            utm_epsg (int): UTM EPSG code for processing
            
        Returns:
            bool: Success status
        """
        print("\nSTEP 3: Lane Geometry Creation")
        print("-" * 40)
        
        if self.osm_data is None or self.osm_data.empty:
            print("No OSM data available for lane creation")
            return False
        
        try:
            # Filter highways
            highways = self.osm_data[self.osm_data["highway"].notna()].copy() if "highway" in self.osm_data.columns else gpd.GeoDataFrame(geometry=[], crs="EPSG:4326")
            markings = self.osm_data[self.osm_data["road_marking"].notna()].copy() if "road_marking" in self.osm_data.columns else gpd.GeoDataFrame(geometry=[], crs="EPSG:4326")
            
            # Filter LineString geometries only
            highways = highways[highways.geometry.type.isin(['LineString', 'MultiLineString'])].copy()
            
            if highways.empty:
                print("No highway LineString features found")
                return False
            
            print(f"Processing {len(highways)} highway features")
            
            # Create lane geometries
            crs_m = CRS.from_epsg(utm_epsg)
            lanes_gdf = create_lane_geometries(highways, crs_m)
            
            # Save lane geometries
            lanes_output_dir = self.output_dir / "lane_geometries"
            lanes_output_dir.mkdir(exist_ok=True)
            
            # Save individual lane files
            lanes_file = lanes_output_dir / "lanes.geojson"
            lanes_gdf.to_file(lanes_file, driver="GeoJSON")
            print(f"Lane geometries saved: {lanes_file}")
            
            # Create combined output with enhanced styling
            combined_file = lanes_output_dir / "lanes_and_markings.geojson"
            create_geojson_output(lanes_gdf, markings, str(combined_file))
            print(f"Combined lanes and markings saved: {combined_file}")
            
            return True
            
        except Exception as e:
            print(f"Error creating lane geometries: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def combine_lidar_osm_features(self, output_crs: str = "EPSG:4326") -> bool:
        """
        Combine LiDAR and OSM features into integrated output
        
        Args:
            output_crs (str): Output coordinate system
            
        Returns:
            bool: Success status
        """
        print("\nSTEP 4: Combining LiDAR and OSM Features")
        print("-" * 40)
        
        try:
            combined_output_dir = self.output_dir / "combined_features"
            combined_output_dir.mkdir(exist_ok=True)
            
            # Load LiDAR features
            lidar_dir = self.output_dir / "lidar_features"
            lidar_files = {
                'polygons': lidar_dir / "polygons.geojson",
                'lines': lidar_dir / "lines.geojson", 
                'points': lidar_dir / "points.geojson"
            }
            
            # Load OSM features
            osm_dir = self.output_dir / "lane_geometries"
            osm_files = {
                'lanes': osm_dir / "lanes.geojson",
                'combined_lanes': osm_dir / "lanes_and_markings.geojson"
            }
            
            # Create integrated GeoJSON
            all_features = []
            
            # Add LiDAR features
            for feature_type, file_path in lidar_files.items():
                if file_path.exists():
                    gdf = gpd.read_file(file_path)
                    
                    # Transform to output CRS if needed
                    if gdf.crs != output_crs:
                        gdf = gdf.to_crs(output_crs)
                    
                    for _, row in gdf.iterrows():
                        properties = dict(row.drop('geometry'))

                        # Clean properties - convert NaN to None and handle numpy types
                        cleaned_properties = {}
                        for key, value in properties.items():
                            if pd.isna(value):
                                cleaned_properties[key] = None
                            elif hasattr(value, 'item'):  # numpy scalar
                                cleaned_properties[key] = value.item()
                            else:
                                cleaned_properties[key] = value

                        cleaned_properties['data_source'] = 'lidar'
                        cleaned_properties['feature_type'] = feature_type
                        
                        # Add styling for LiDAR features
                        if feature_type == 'polygons':
                            cleaned_properties['stroke'] = '#FF0000'  # Red for LiDAR polygons
                            cleaned_properties['fill'] = '#FF0000'
                            cleaned_properties['fill-opacity'] = 0.3
                            cleaned_properties['stroke-width'] = 2
                        elif feature_type == 'lines':
                            cleaned_properties['stroke'] = '#0000FF'  # Blue for LiDAR lines
                            cleaned_properties['stroke-width'] = 2
                            cleaned_properties['stroke-opacity'] = 0.8
                        else:  # points
                            cleaned_properties['marker-color'] = '#FFFF00'  # Yellow for LiDAR points
                            cleaned_properties['marker-size'] = 'small'
                        
                        feature = {
                            "type": "Feature",
                            "geometry": row.geometry.__geo_interface__,
                            "properties": cleaned_properties
                        }
                        all_features.append(feature)
            
            # Add OSM lane features
            if osm_files['lanes'].exists():
                lanes_gdf = gpd.read_file(osm_files['lanes'])
                
                # Transform to output CRS if needed
                if lanes_gdf.crs != output_crs:
                    lanes_gdf = lanes_gdf.to_crs(output_crs)
                
                for _, row in lanes_gdf.iterrows():
                    properties = dict(row.drop('geometry'))

                    # Clean properties - convert NaN to None and handle numpy types
                    cleaned_properties = {}
                    for key, value in properties.items():
                        if pd.isna(value):
                            cleaned_properties[key] = None
                        elif hasattr(value, 'item'):  # numpy scalar
                            cleaned_properties[key] = value.item()
                        else:
                            cleaned_properties[key] = value

                    cleaned_properties['data_source'] = 'osm'
                    cleaned_properties['feature_type'] = 'lane'
                    
                    # Keep existing OSM styling or add default
                    if 'stroke' not in cleaned_properties:
                        cleaned_properties['stroke'] = '#00FF00'  # Green for OSM lanes
                        cleaned_properties['stroke-width'] = 1.5
                        cleaned_properties['stroke-opacity'] = 1.0

                    feature = {
                        "type": "Feature",
                        "geometry": row.geometry.__geo_interface__,
                        "properties": cleaned_properties
                    }
                    all_features.append(feature)
            
            # Create combined GeoJSON
            combined_geojson = {
                "type": "FeatureCollection",
                "name": "lidar_osm_integrated",
                "crs": {
                    "type": "name",
                    "properties": {"name": f"urn:ogc:def:crs:OGC:1.3:CRS84" if output_crs == "EPSG:4326" else f"urn:ogc:def:crs:EPSG::{output_crs.split(':')[1]}"}
                },
                "features": all_features
            }
            
            # Save combined file
            combined_file = combined_output_dir / "integrated_lidar_osm.geojson"
            with open(combined_file, 'w', encoding='utf-8') as f:
                json.dump(combined_geojson, f, ensure_ascii=False, indent=2)
            
            print(f"Integrated features saved: {combined_file}")
            print(f"Total features: {len(all_features)}")
            
            # Create summary
            lidar_count = sum(1 for f in all_features if f['properties']['data_source'] == 'lidar')
            osm_count = sum(1 for f in all_features if f['properties']['data_source'] == 'osm')
            
            print(f"  LiDAR features: {lidar_count}")
            print(f"  OSM features: {osm_count}")
            
            return True
            
        except Exception as e:
            print(f"Error combining features: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_integrated_pipeline(self,
                              chunk_size: float = 500.0,
                              utm_epsg: int = 25832,
                              processing_crs: str = "EPSG:25832",
                              output_crs: str = "EPSG:4326",
                              sample_rate: float = 0.01,
                              alpha: float = 100.0) -> bool:
        """
        Run the complete integrated pipeline
        
        Args:
            chunk_size (float): LiDAR chunk size in meters
            utm_epsg (int): UTM EPSG code
            processing_crs (str): Processing coordinate system
            output_crs (str): Output coordinate system
            sample_rate (float): LAS sampling rate
            alpha (float): Alpha shape parameter
            
        Returns:
            bool: Success status
        """
        print("INTEGRATED LIDAR + OSM PIPELINE")
        print("=" * 60)
        print(f"Input LAS: {self.las_file}")
        print(f"Output directory: {self.output_dir}")
        print(f"Processing CRS: {processing_crs}")
        print(f"Output CRS: {output_crs}")
        print("=" * 60)
        
        start_time = time.time()
        
        # Step 1: Extract LiDAR features
        if not self.extract_lidar_features(chunk_size, processing_crs):
            print("Failed to extract LiDAR features")
            return False
        
        # Step 2: Extract OSM features
        if not self.extract_osm_features(utm_epsg, sample_rate, alpha):
            print("Failed to extract OSM features")
            return False
        
        # Step 3: Create lane geometries
        if not self.create_lane_geometries(utm_epsg):
            print("Failed to create lane geometries")
            return False
        
        # Step 4: Combine features
        if not self.combine_lidar_osm_features(output_crs):
            print("Failed to combine features")
            return False
        
        # Create web-compatible version if needed
        if output_crs != "EPSG:4326":
            print("\nSTEP 5: Creating Web-Compatible Version")
            print("-" * 40)
            
            web_dir = self.output_dir / "web_compatible"
            web_dir.mkdir(exist_ok=True)
            
            # Transform combined file to WGS84
            combined_file = self.output_dir / "combined_features" / "integrated_lidar_osm.geojson"
            web_file = web_dir / "integrated_lidar_osm_wgs84.geojson"
            
            source_epsg = int(output_crs.split(':')[1])
            if transform_geojson(str(combined_file), str(web_file), source_epsg, 4326):
                print(f"Web-compatible version saved: {web_file}")
        
        # Summary
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n{'='*60}")
        print("INTEGRATED PIPELINE COMPLETED SUCCESSFULLY!")
        print(f"{'='*60}")
        print(f"Total execution time: {duration:.2f} seconds")
        print(f"Output directory: {self.output_dir}")
        print()
        print("Generated directories:")
        print("  - lidar_features/ - LiDAR-extracted polygons, lines, points")
        print("  - osm_features/ - Raw OpenStreetMap data")
        print("  - lane_geometries/ - OSM lane geometries with styling")
        print("  - combined_features/ - Integrated LiDAR + OSM features")
        if output_crs != "EPSG:4326":
            print("  - web_compatible/ - WGS84 version for web mapping")
        
        print()
        print("Key files for visualization:")
        print("  - combined_features/integrated_lidar_osm.geojson")
        if output_crs != "EPSG:4326":
            print("  - web_compatible/integrated_lidar_osm_wgs84.geojson (for geojson.io)")
        
        return True

def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description='Integrated LiDAR + OSM Pipeline',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage with filtered LAS file
  python lidar_osm_integrated_pipeline.py -i ../FusedLidar_Lidar1_UTM32N_filtered.las -o output/
  
  # Custom parameters
  python lidar_osm_integrated_pipeline.py -i ../FusedLidar_Lidar1_UTM32N_filtered.las -o output/ \\
    --chunk-size 1000 --utm-epsg 25832 --output-crs EPSG:4326
  
  # High detail OSM extraction
  python lidar_osm_integrated_pipeline.py -i ../FusedLidar_Lidar1_UTM32N_filtered.las -o output/ \\
    --sample-rate 0.05 --alpha 50
        """
    )
    
    parser.add_argument('--input', '-i', required=True, help='Input LAS file path')
    parser.add_argument('--output', '-o', default='integrated_output', help='Output directory')
    parser.add_argument('--chunk-size', type=float, default=500.0, help='LiDAR chunk size in meters')
    parser.add_argument('--utm-epsg', type=int, default=25832, help='UTM EPSG code')
    parser.add_argument('--processing-crs', default='EPSG:25832', help='Processing CRS')
    parser.add_argument('--output-crs', default='EPSG:4326', help='Output CRS')
    parser.add_argument('--sample-rate', type=float, default=0.01, help='LAS sampling rate for boundary')
    parser.add_argument('--alpha', type=float, default=100.0, help='Alpha shape parameter')
    
    args = parser.parse_args()
    
    # Initialize and run pipeline
    pipeline = IntegratedLiDARPipeline(args.input, args.output)
    success = pipeline.run_integrated_pipeline(
        chunk_size=args.chunk_size,
        utm_epsg=args.utm_epsg,
        processing_crs=args.processing_crs,
        output_crs=args.output_crs,
        sample_rate=args.sample_rate,
        alpha=args.alpha
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
