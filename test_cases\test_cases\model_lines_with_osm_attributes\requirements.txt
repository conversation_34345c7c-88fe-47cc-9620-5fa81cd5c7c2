# Model Lines with OSM Attributes Pipeline Requirements

# Core data processing
numpy>=1.21.0
pandas>=1.3.0
geopandas>=0.10.0
shapely>=1.8.0

# LiDAR processing
laspy>=2.0.0

# Machine learning
scikit-learn>=1.0.0
scipy>=1.7.0

# Coordinate transformations
pyproj>=3.2.0

# OpenStreetMap data
overpy>=0.6

# Visualization (optional)
matplotlib>=3.5.0

# PDAL (install separately with conda)
# conda install -c conda-forge pdal
