#!/usr/bin/env python3
"""
GeoJSON Generator for LiDAR Data
Processes chunked LiDAR data to extract polygons and line strings
"""

import numpy as np
import json
import os
from pathlib import Path
from typing import List, Dict, Any, Tuple
import geopandas as gpd
from shapely.geometry import Point, LineString, Polygon, MultiPoint
from sklearn.cluster import DBSCAN
from sklearn.decomposition import PCA
from sklearn.linear_model import RANSACRegressor, LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline
import pandas as pd

class GeoJSONGenerator:
    """Generate GeoJSON features from LiDAR chunks"""
    
    def __init__(self, chunks_dir: str, output_crs: str = "EPSG:25834"):
        """
        Initialize the GeoJSON generator

        Args:
            chunks_dir (str): Directory containing chunk files
            output_crs (str): Output coordinate reference system
        """
        self.chunks_dir = Path(chunks_dir)
        self.output_crs = output_crs
        self.processing_info = None
        self.all_polygons = []
        self.all_lines = []
        self.all_points = []
        
    def load_processing_info(self) -> bool:
        """Load chunk processing information"""
        info_file = self.chunks_dir / "processing_info.json"
        
        if not info_file.exists():
            print(f"❌ Processing info file not found: {info_file}")
            return False
        
        try:
            with open(info_file, 'r') as f:
                self.processing_info = json.load(f)
            
            print(f"Loaded processing info:")
            print(f"   Source: {self.processing_info['source_file']}")
            print(f"   Total chunks: {self.processing_info['total_chunks']}")
            print(f"   Processed chunks: {self.processing_info['processed_chunks']}")

            return True

        except Exception as e:
            print(f"Error loading processing info: {str(e)}")
            return False
    
    def process_chunk_for_features(self, chunk_data: np.ndarray, chunk_id: int) -> Tuple[List, List, List]:
        """
        Process a single chunk to extract features
        
        Args:
            chunk_data (np.ndarray): Point cloud data [x, y, z, intensity, return_num, num_returns, classification]
            chunk_id (int): Chunk identifier
            
        Returns:
            Tuple of (polygons, lines, points) lists
        """
        if len(chunk_data) == 0:
            return [], [], []
        
        # Convert to DataFrame for easier processing
        df = pd.DataFrame(chunk_data, columns=['X', 'Y', 'Z', 'Intensity', 'ReturnNum', 'NumReturns', 'Classification'])
        
        # Filter by intensity (already filtered by PDAL, but double-check)
        df = df[df['Intensity'] >= 70].copy()
        
        if len(df) < 10:  # Not enough points for meaningful clustering
            return [], [], []
        
        # Ground plane filtering using RANSAC
        XY = df[['X', 'Y']].values
        Z = df['Z'].values
        
        try:
            plane_model = make_pipeline(
                PolynomialFeatures(degree=1, include_bias=True),
                RANSACRegressor(
                    estimator=LinearRegression(),
                    residual_threshold=0.1,  # 10cm threshold
                    random_state=42
                )
            )
            plane_model.fit(XY, Z)
            Z_pred = plane_model.predict(XY)
            
            # Keep points close to ground plane
            df['residual'] = np.abs(Z - Z_pred)
            df = df[df['residual'] <= 0.1].copy()
            
        except Exception as e:
            print(f"   Warning: RANSAC failed for chunk {chunk_id}: {str(e)}")
            # Continue without ground filtering
        
        if len(df) < 10:
            return [], [], []
        
        # DBSCAN clustering
        coords = df[['X', 'Y']].values
        
        # Adaptive eps based on point density
        area = (coords[:, 0].max() - coords[:, 0].min()) * (coords[:, 1].max() - coords[:, 1].min())
        density = len(coords) / max(area, 1)
        eps = max(0.5, min(2.0, 10.0 / np.sqrt(density)))  # Adaptive eps
        
        db = DBSCAN(eps=eps, min_samples=5).fit(coords)
        df['cluster'] = db.labels_
        
        chunk_polygons = []
        chunk_lines = []
        chunk_points = []
        
        # Process each cluster
        for cluster_id in sorted(df['cluster'].unique()):
            if cluster_id < 0:  # Skip noise points
                continue
            
            cluster_points = df[df['cluster'] == cluster_id][['X', 'Y']].values
            
            if len(cluster_points) < 5:
                continue
            
            try:
                # Create convex hull (polygon)
                multipoint = MultiPoint(cluster_points)
                hull = multipoint.convex_hull
                
                if hull.geom_type == 'Polygon' and hull.area > 1.0:  # Minimum area threshold
                    chunk_polygons.append({
                        'geometry': hull,
                        'properties': {
                            'chunk_id': chunk_id,
                            'cluster_id': cluster_id,
                            'point_count': len(cluster_points),
                            'area': hull.area
                        }
                    })
                
                # Create centerline using PCA
                if len(cluster_points) >= 10:
                    pca = PCA(n_components=2).fit(cluster_points)
                    center = pca.mean_
                    direction = pca.components_[0]
                    
                    # Extend line along principal direction
                    span = max(hull.bounds[2] - hull.bounds[0], hull.bounds[3] - hull.bounds[1]) * 1.5
                    
                    line_start = center - direction * span / 2
                    line_end = center + direction * span / 2
                    
                    line = LineString([line_start, line_end])
                    
                    # Intersect with hull to get actual centerline
                    centerline = hull.intersection(line)
                    
                    if centerline.geom_type == 'LineString' and centerline.length > 2.0:
                        chunk_lines.append({
                            'geometry': centerline,
                            'properties': {
                                'chunk_id': chunk_id,
                                'cluster_id': cluster_id,
                                'length': centerline.length,
                                'point_count': len(cluster_points)
                            }
                        })
                    elif centerline.geom_type == 'MultiLineString':
                        # Take the longest segment
                        longest = max(centerline.geoms, key=lambda x: x.length)
                        if longest.length > 2.0:
                            chunk_lines.append({
                                'geometry': longest,
                                'properties': {
                                    'chunk_id': chunk_id,
                                    'cluster_id': cluster_id,
                                    'length': longest.length,
                                    'point_count': len(cluster_points)
                                }
                            })
                
            except Exception as e:
                print(f"   Warning: Error processing cluster {cluster_id} in chunk {chunk_id}: {str(e)}")
                continue
        
        # Sample representative points
        if len(df) > 0:
            # Sample every 10th point or max 100 points per chunk
            sample_size = min(100, max(1, len(df) // 10))
            sampled_df = df.sample(n=sample_size, random_state=42)
            
            for _, row in sampled_df.iterrows():
                chunk_points.append({
                    'geometry': Point(row['X'], row['Y']),
                    'properties': {
                        'chunk_id': chunk_id,
                        'intensity': row['Intensity'],
                        'z': row['Z'],
                        'classification': row['Classification']
                    }
                })
        
        return chunk_polygons, chunk_lines, chunk_points
    
    def process_all_chunks(self) -> bool:
        """Process all chunks to extract features"""
        if not self.processing_info:
            print("Processing info not loaded")
            return False

        print(f"Processing chunks for feature extraction...")
        
        chunk_files = list(self.chunks_dir.glob("chunk_*.npy"))
        
        for i, chunk_file in enumerate(chunk_files):
            chunk_id = int(chunk_file.stem.split('_')[1])
            
            print(f"   Processing chunk {i+1}/{len(chunk_files)} (ID: {chunk_id})", end="")
            
            try:
                # Load chunk data
                chunk_data = np.load(chunk_file)
                
                # Extract features
                polygons, lines, points = self.process_chunk_for_features(chunk_data, chunk_id)
                
                # Add to collections
                self.all_polygons.extend(polygons)
                self.all_lines.extend(lines)
                self.all_points.extend(points)
                
                print(f" - {len(polygons)} polygons, {len(lines)} lines, {len(points)} points")
                
            except Exception as e:
                print(f" - Error: {str(e)}")
                continue
        
        print(f"Feature extraction complete!")
        print(f"   Total polygons: {len(self.all_polygons)}")
        print(f"   Total lines: {len(self.all_lines)}")
        print(f"   Total points: {len(self.all_points)}")
        
        return True
    
    def save_geojson(self, output_dir: str = "geojson_output") -> bool:
        """Save extracted features as GeoJSON files"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        try:
            # Determine source CRS from processing info
            source_crs = "EPSG:25832"  # UTM32N as specified in pipeline
            
            # Create GeoDataFrames
            if self.all_polygons:
                polygons_gdf = gpd.GeoDataFrame([
                    {**item['properties'], 'geometry': item['geometry']} 
                    for item in self.all_polygons
                ], crs=source_crs)
                
                # Reproject to output CRS
                polygons_gdf = polygons_gdf.to_crs(self.output_crs)
                
                # Save to GeoJSON
                polygons_file = output_path / "polygons.geojson"
                polygons_gdf.to_file(polygons_file, driver="GeoJSON")
                print(f"Saved polygons: {polygons_file}")

            if self.all_lines:
                lines_gdf = gpd.GeoDataFrame([
                    {**item['properties'], 'geometry': item['geometry']}
                    for item in self.all_lines
                ], crs=source_crs)

                # Reproject to output CRS
                lines_gdf = lines_gdf.to_crs(self.output_crs)

                # Save to GeoJSON
                lines_file = output_path / "lines.geojson"
                lines_gdf.to_file(lines_file, driver="GeoJSON")
                print(f"Saved lines: {lines_file}")

            if self.all_points:
                points_gdf = gpd.GeoDataFrame([
                    {**item['properties'], 'geometry': item['geometry']}
                    for item in self.all_points
                ], crs=source_crs)

                # Reproject to output CRS
                points_gdf = points_gdf.to_crs(self.output_crs)

                # Save to GeoJSON
                points_file = output_path / "points.geojson"
                points_gdf.to_file(points_file, driver="GeoJSON")
                print(f"Saved points: {points_file}")
            
            # Save combined GeoJSON
            combined_features = []
            
            for item in self.all_polygons:
                # Convert numpy types to Python native types for JSON serialization
                properties = {}
                for key, value in item['properties'].items():
                    if hasattr(value, 'item'):  # numpy scalar
                        properties[key] = value.item()
                    else:
                        properties[key] = value

                feature = {
                    "type": "Feature",
                    "geometry": item['geometry'].__geo_interface__,
                    "properties": {**properties, "feature_type": "polygon"}
                }
                combined_features.append(feature)

            for item in self.all_lines:
                # Convert numpy types to Python native types for JSON serialization
                properties = {}
                for key, value in item['properties'].items():
                    if hasattr(value, 'item'):  # numpy scalar
                        properties[key] = value.item()
                    else:
                        properties[key] = value

                feature = {
                    "type": "Feature",
                    "geometry": item['geometry'].__geo_interface__,
                    "properties": {**properties, "feature_type": "line"}
                }
                combined_features.append(feature)
            
            combined_geojson = {
                "type": "FeatureCollection",
                "crs": {"type": "name", "properties": {"name": self.output_crs}},
                "features": combined_features
            }
            
            combined_file = output_path / "combined_features.geojson"
            with open(combined_file, 'w') as f:
                json.dump(combined_geojson, f, indent=2)
            
            print(f"Saved combined features: {combined_file}")

            return True

        except Exception as e:
            print(f"Error saving GeoJSON: {str(e)}")
            return False

def main():
    """Main function"""
    script_dir = Path(__file__).parent
    chunks_dir = script_dir / "chunks"
    
    if not chunks_dir.exists():
        print(f"Chunks directory not found: {chunks_dir}")
        print("Please run chunk_processor.py first!")
        return

    print("GeoJSON Generator for LiDAR Data")
    print("=" * 50)
    
    # Initialize generator
    generator = GeoJSONGenerator(str(chunks_dir), output_crs="EPSG:25834")
    
    # Load processing info and process chunks
    if generator.load_processing_info():
        if generator.process_all_chunks():
            output_dir = script_dir / "geojson_output"
            if generator.save_geojson(str(output_dir)):
                print(f"\nGeoJSON generation completed!")
                print(f"Output directory: {output_dir}")
            else:
                print("Failed to save GeoJSON files!")
        else:
            print("Failed to process chunks!")
    else:
        print("Failed to load processing info!")

if __name__ == "__main__":
    main()
