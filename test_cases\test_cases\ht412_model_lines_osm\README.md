# HT412 Model Line Strings with OSM Attributes Pipeline

This pipeline extracts line strings from the HT412 LiDAR data using machine learning models and enriches them with OpenStreetMap (OSM) attributes and tags.

## Input File
- **LAS File**: `HT412_1738935654_3217135_1422974157258029_1422974185645127_Clip.las`
- **Intensity Range**: 20-45 (different from previous datasets)
- **Size**: ~151 MB

## Key Features

1. **Model-Generated Line Strings**: Uses LiDAR point cloud processing to extract line strings through clustering and PCA analysis
2. **OSM Attribute Enrichment**: Queries OSM for road/highway data in the area and associates attributes with model-generated lines
3. **Coordinate Transformation**: Ensures all outputs are in EPSG:25832 (UTM Zone 32N) and WGS84 for web viewing
4. **Comprehensive GeoJSON Output**: Generates GeoJSON files with all OSM tags and attributes

## Pipeline Steps

1. **Intensity Verification**: Check that LiDAR data has intensity values in range 20-45
2. **Model Line Extraction**: Extract line strings from LiDAR using clustering and PCA
3. **OSM Data Retrieval**: Query OSM for highway/road data in the area
4. **Attribute Association**: Match model lines with nearby OSM features and transfer attributes
5. **GeoJSON Generation**: Create final GeoJSON with enriched line strings
6. **WGS84 Transformation**: Convert to WGS84 for geojson.io compatibility

## Files

- `run_model_lines_pipeline.py` - Main pipeline runner
- `check_intensity.py` - Verify intensity range 20-45
- `model_line_extractor.py` - Extract line strings from LiDAR using ML models
- `osm_attribute_enricher.py` - Query OSM and associate attributes with model lines
- `geojson_generator.py` - Generate final GeoJSON outputs
- `transform_to_wgs84.py` - Transform coordinates to WGS84
- `config.json` - Configuration file

## Usage

```bash
# Check intensity values first
python check_intensity.py

# Run complete pipeline
python run_model_lines_pipeline.py

# Transform to WGS84 for web viewing
python transform_to_wgs84.py
```

## Output

All outputs are saved in this folder:
- `model_lines_with_osm_final_wgs84.geojson` - Main output for geojson.io (WGS84)
- `model_lines_with_osm_final.geojson` - Main output (UTM32N)
- `model_lines_only.geojson` - Model-generated lines without OSM attributes
- `osm_raw_data.geojson` - Raw OSM data for reference
- `processing_statistics.json` - Detailed statistics
- `processing_summary.txt` - Human-readable summary
