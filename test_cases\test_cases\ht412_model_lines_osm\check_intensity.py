#!/usr/bin/env python3
"""
Check intensity values in HT412 LAS file to verify expected range 20-45
"""

import laspy
import numpy as np
from pathlib import Path

def check_intensity_values(las_file):
    """Check intensity statistics in LAS file"""
    print(f"[INFO] Checking intensity values in: {las_file}")
    
    if not Path(las_file).exists():
        print(f"[ERROR] File not found: {las_file}")
        return False
    
    # Read LAS file
    las = laspy.read(las_file)
    
    # Get basic info
    print(f"[INFO] Total points: {len(las.xyz):,}")
    
    # Check if intensity is available
    if hasattr(las, 'intensity'):
        intensity = las.intensity
        
        print(f"[INFO] Intensity statistics:")
        print(f"  Min intensity: {intensity.min()}")
        print(f"  Max intensity: {intensity.max()}")
        print(f"  Mean intensity: {intensity.mean():.2f}")
        print(f"  Std intensity: {intensity.std():.2f}")
        
        # Check expected range 20-45
        in_range = np.sum((intensity >= 20) & (intensity <= 45))
        total_points = len(intensity)
        percentage = (in_range / total_points) * 100
        
        print(f"[INFO] Points with intensity 20-45: {in_range:,} ({percentage:.1f}%)")
        
        # Show distribution
        ranges = [
            (0, 19, "Below 20"),
            (20, 45, "Target range (20-45)"),
            (46, 100, "46-100"),
            (101, 255, "Above 100")
        ]
        
        print(f"[INFO] Intensity distribution:")
        for min_val, max_val, label in ranges:
            count = np.sum((intensity >= min_val) & (intensity <= max_val))
            pct = (count / total_points) * 100
            print(f"  {label}: {count:,} ({pct:.1f}%)")
        
        # Check if data needs filtering
        if intensity.min() < 20 or intensity.max() > 45:
            print(f"[WARNING] ⚠️  Data contains points outside 20-45 range")
            print(f"[INFO] You may want to filter the data to intensity 20-45")
            return False
        else:
            print(f"[SUCCESS] ✅ All intensity values are in expected range 20-45!")
            return True
    else:
        print(f"[ERROR] No intensity data found in LAS file")
        return False

def main():
    """Main execution"""
    las_file = "D:/Integrate/HT412_1738935654_3217135_1422974157258029_1422974185645127_Clip.las"
    
    print("HT412 LAS File Intensity Check")
    print("=" * 50)
    print("Expected intensity range: 20-45")
    print("=" * 50)
    
    check_intensity_values(las_file)

if __name__ == "__main__":
    main()
