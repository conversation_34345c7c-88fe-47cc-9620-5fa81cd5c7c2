#!/usr/bin/env python3
"""
Transform GeoJSON from EPSG:25832 to WGS84 (EPSG:4326)
For viewing in geojson.io and other web mapping tools
"""

import json
import geopandas as gpd
from pathlib import Path
from pyproj import Transformer

def transform_geojson_to_wgs84(input_file, output_file):
    """Transform GeoJSON from UTM32N to WGS84"""
    print(f"[INFO] Transforming {input_file} to WGS84...")
    
    # Read the GeoJSON file
    gdf = gpd.read_file(input_file)
    
    print(f"[INFO] Original CRS: {gdf.crs}")
    print(f"[INFO] Features: {len(gdf)}")
    
    # Transform to WGS84
    gdf_wgs84 = gdf.to_crs("EPSG:4326")
    
    # Save as GeoJSON
    gdf_wgs84.to_file(output_file, driver="GeoJSON")
    
    print(f"[SUCCESS] Transformed to WGS84: {output_file}")
    
    # Show sample coordinates
    if len(gdf_wgs84) > 0:
        sample_geom = gdf_wgs84.iloc[0].geometry
        if hasattr(sample_geom, 'coords'):
            coords = list(sample_geom.coords)
            if coords:
                lon, lat = coords[0]
                print(f"[INFO] Sample coordinates: {lon:.6f}, {lat:.6f}")
    
    return output_file

def main():
    """Main execution function"""
    print("[INFO] Starting WGS84 Transformation")
    print("-" * 50)
    
    # Configuration
    script_dir = Path(__file__).parent
    
    # Input and output files
    input_files = [
        "model_lines_with_osm.geojson",
        "model_lines_with_osm_final.geojson",
        "model_lines_only.geojson",
        "osm_raw_data.geojson"
    ]
    
    transformed_files = []
    
    for input_filename in input_files:
        input_file = script_dir / input_filename
        
        if input_file.exists():
            # Create WGS84 version
            output_filename = input_filename.replace('.geojson', '_wgs84.geojson')
            output_file = script_dir / output_filename
            
            try:
                transform_geojson_to_wgs84(input_file, output_file)
                transformed_files.append(output_filename)
            except Exception as e:
                print(f"[ERROR] Failed to transform {input_filename}: {str(e)}")
        else:
            print(f"[WARNING] File not found: {input_filename}")
    
    print(f"\n[SUCCESS] WGS84 transformation completed!")
    print(f"[INFO] Transformed {len(transformed_files)} files:")
    
    for filename in transformed_files:
        file_path = script_dir / filename
        if file_path.exists():
            file_size = file_path.stat().st_size / 1024  # KB
            print(f"   - {filename} ({file_size:.1f} KB)")
    
    print(f"\n[INFO] Files ready for geojson.io:")
    print(f"   - Main file: model_lines_with_osm_final_wgs84.geojson")
    print(f"   - Model lines only: model_lines_only_wgs84.geojson")
    print(f"   - OSM data: osm_raw_data_wgs84.geojson")

if __name__ == "__main__":
    main()
