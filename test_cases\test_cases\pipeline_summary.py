#!/usr/bin/env python3
"""
Pipeline Summary Report
Shows the results of the LiDAR processing pipeline
"""

import json
import os
from pathlib import Path
import geopandas as gpd

def analyze_geojson_files():
    """Analyze the generated GeoJSON files"""
    script_dir = Path(__file__).parent
    output_dir = script_dir / "geojson_output"
    
    if not output_dir.exists():
        print("No GeoJSON output directory found!")
        return
    
    print("LIDAR PROCESSING PIPELINE RESULTS")
    print("=" * 60)
    
    # Check processing info
    chunks_dir = script_dir / "chunks"
    if chunks_dir.exists():
        info_file = chunks_dir / "processing_info.json"
        if info_file.exists():
            with open(info_file, 'r') as f:
                info = json.load(f)
            
            print(f"SOURCE DATA:")
            print(f"  Input file: {info['source_file']}")
            print(f"  Chunk size: {info['chunk_size_meters']}m x {info['chunk_size_meters']}m")
            print(f"  Total chunks: {info['total_chunks']}")
            print(f"  Processed chunks: {info['processed_chunks']}")
            print(f"  Bounds: X({info['bounds']['min_x']:.2f} to {info['bounds']['max_x']:.2f})")
            print(f"          Y({info['bounds']['min_y']:.2f} to {info['bounds']['max_y']:.2f})")
            print(f"          Z({info['bounds']['min_z']:.2f} to {info['bounds']['max_z']:.2f})")
    
    print(f"\nGENERATED GEOJSON FILES:")
    
    # Analyze each GeoJSON file
    geojson_files = {
        'polygons.geojson': 'Polygon Features',
        'lines.geojson': 'Line String Features', 
        'points.geojson': 'Point Features',
        'combined_features.geojson': 'Combined Features'
    }
    
    total_features = 0
    
    for filename, description in geojson_files.items():
        filepath = output_dir / filename
        
        if filepath.exists():
            file_size = filepath.stat().st_size / 1024  # KB
            
            try:
                if filename != 'combined_features.geojson':
                    gdf = gpd.read_file(filepath)
                    feature_count = len(gdf)
                    total_features += feature_count
                    
                    print(f"  {filename}:")
                    print(f"    Description: {description}")
                    print(f"    Features: {feature_count:,}")
                    print(f"    File size: {file_size:.1f} KB")
                    
                    if feature_count > 0:
                        # Show some statistics
                        if 'area' in gdf.columns:
                            print(f"    Area range: {gdf['area'].min():.2f} to {gdf['area'].max():.2f}")
                        if 'length' in gdf.columns:
                            print(f"    Length range: {gdf['length'].min():.2f} to {gdf['length'].max():.2f}")
                        if 'point_count' in gdf.columns:
                            print(f"    Point count range: {gdf['point_count'].min():,} to {gdf['point_count'].max():,}")
                        
                        # Show chunk distribution
                        if 'chunk_id' in gdf.columns:
                            chunk_counts = gdf['chunk_id'].value_counts().sort_index()
                            print(f"    Chunks with features: {len(chunk_counts)}")
                else:
                    # For combined file, just show basic info
                    print(f"  {filename}:")
                    print(f"    Description: {description}")
                    print(f"    File size: {file_size:.1f} KB")
                    
            except Exception as e:
                print(f"  {filename}: Error reading file - {str(e)}")
        else:
            print(f"  {filename}: NOT FOUND")
    
    print(f"\nSUMMARY:")
    print(f"  Total individual features: {total_features:,}")
    print(f"  Output coordinate system: EPSG:4326 (WGS84)")
    print(f"  Processing method: Chunked spatial processing")
    print(f"  Feature extraction: DBSCAN clustering + geometric analysis")

def show_sample_features():
    """Show sample features from each type"""
    script_dir = Path(__file__).parent
    output_dir = script_dir / "geojson_output"
    
    print(f"\nSAMPLE FEATURES:")
    print("-" * 40)
    
    # Show sample polygon
    polygons_file = output_dir / "polygons.geojson"
    if polygons_file.exists():
        try:
            gdf = gpd.read_file(polygons_file)
            if len(gdf) > 0:
                sample = gdf.iloc[0]
                print(f"Sample Polygon:")
                print(f"  Chunk ID: {sample['chunk_id']}")
                print(f"  Cluster ID: {sample['cluster_id']}")
                print(f"  Point Count: {sample['point_count']:,}")
                print(f"  Area: {sample['area']:.2f} sq units")
                print(f"  Geometry: {sample.geometry.geom_type}")
        except Exception as e:
            print(f"Error reading polygons: {str(e)}")
    
    # Show sample line
    lines_file = output_dir / "lines.geojson"
    if lines_file.exists():
        try:
            gdf = gpd.read_file(lines_file)
            if len(gdf) > 0:
                sample = gdf.iloc[0]
                print(f"\nSample Line String:")
                print(f"  Chunk ID: {sample['chunk_id']}")
                print(f"  Cluster ID: {sample['cluster_id']}")
                print(f"  Point Count: {sample['point_count']:,}")
                print(f"  Length: {sample['length']:.2f} units")
                print(f"  Geometry: {sample.geometry.geom_type}")
        except Exception as e:
            print(f"Error reading lines: {str(e)}")

def show_usage_instructions():
    """Show how to use the generated files"""
    print(f"\nUSAGE INSTRUCTIONS:")
    print("-" * 40)
    print("The generated GeoJSON files can be used with:")
    print("  • QGIS - Load as vector layers")
    print("  • ArcGIS - Import as feature classes") 
    print("  • Web mapping - Leaflet, OpenLayers, Mapbox")
    print("  • Python - geopandas, shapely, folium")
    print("  • R - sf, leaflet packages")
    print("  • PostGIS - Import using ogr2ogr")
    print("\nExample Python usage:")
    print("  import geopandas as gpd")
    print("  polygons = gpd.read_file('polygons.geojson')")
    print("  lines = gpd.read_file('lines.geojson')")
    print("  print(f'Found {len(polygons)} polygons and {len(lines)} lines')")

def main():
    """Main function"""
    analyze_geojson_files()
    show_sample_features()
    show_usage_instructions()

if __name__ == "__main__":
    main()
