#!/usr/bin/env python3
"""
Create Combined WGS84 GeoJSON File
Combines individual polygon, line, and point GeoJSON files into one
"""

import json
import geopandas as gpd
from pathlib import Path

def create_combined_geojson():
    """Create a combined GeoJSON file from individual files"""
    
    script_dir = Path(__file__).parent
    input_dir = script_dir / "geojson_output_wgs84"
    
    if not input_dir.exists():
        print(f"Input directory not found: {input_dir}")
        return
    
    print("Creating combined WGS84 GeoJSON file...")
    print("=" * 50)
    
    combined_features = []
    
    # Files to combine
    files_to_combine = [
        ('polygons.geojson', 'polygon'),
        ('lines.geojson', 'line'),
        ('points.geojson', 'point')
    ]
    
    total_features = 0
    
    for filename, feature_type in files_to_combine:
        filepath = input_dir / filename
        
        if filepath.exists():
            try:
                print(f"Processing {filename}...")
                
                # Read the GeoJSON file
                gdf = gpd.read_file(filepath)
                
                print(f"  Features: {len(gdf):,}")
                
                # Add each feature to combined list
                for _, row in gdf.iterrows():
                    # Create properties dictionary
                    properties = dict(row.drop('geometry'))
                    properties['feature_type'] = feature_type
                    
                    # Create feature
                    feature = {
                        "type": "Feature",
                        "geometry": row.geometry.__geo_interface__,
                        "properties": properties
                    }
                    
                    combined_features.append(feature)
                
                total_features += len(gdf)
                
            except Exception as e:
                print(f"  Error processing {filename}: {str(e)}")
        else:
            print(f"File not found: {filepath}")
    
    if combined_features:
        # Create combined GeoJSON
        combined_geojson = {
            "type": "FeatureCollection",
            "crs": {
                "type": "name",
                "properties": {
                    "name": "urn:ogc:def:crs:OGC:1.3:CRS84"
                }
            },
            "features": combined_features
        }
        
        # Save combined file
        output_file = input_dir / "combined_features.geojson"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(combined_geojson, f, ensure_ascii=False, indent=2)
            
            file_size = output_file.stat().st_size / 1024  # KB
            
            print(f"\nCombined file created successfully!")
            print(f"  Output: {output_file}")
            print(f"  Total features: {total_features:,}")
            print(f"  File size: {file_size:.1f} KB")
            
            # Show feature breakdown
            feature_counts = {}
            for feature in combined_features:
                ftype = feature['properties']['feature_type']
                feature_counts[ftype] = feature_counts.get(ftype, 0) + 1
            
            print(f"  Feature breakdown:")
            for ftype, count in feature_counts.items():
                print(f"    {ftype}: {count:,}")
            
        except Exception as e:
            print(f"Error saving combined file: {str(e)}")
    
    else:
        print("No features found to combine!")

def show_sample_coordinates():
    """Show sample coordinates to verify transformation"""
    script_dir = Path(__file__).parent
    input_dir = script_dir / "geojson_output_wgs84"
    
    print(f"\nSAMPLE COORDINATES (WGS84):")
    print("-" * 40)
    
    # Check polygon coordinates
    polygons_file = input_dir / "polygons.geojson"
    if polygons_file.exists():
        try:
            gdf = gpd.read_file(polygons_file)
            if len(gdf) > 0:
                sample_geom = gdf.iloc[0].geometry
                if hasattr(sample_geom, 'exterior'):
                    coords = list(sample_geom.exterior.coords)
                    print(f"Sample Polygon coordinates:")
                    print(f"  First point: [{coords[0][0]:.8f}, {coords[0][1]:.8f}]")
                    print(f"  Last point: [{coords[-1][0]:.8f}, {coords[-1][1]:.8f}]")
                    print(f"  Total vertices: {len(coords)}")
        except Exception as e:
            print(f"Error reading polygon sample: {str(e)}")
    
    # Check line coordinates  
    lines_file = input_dir / "lines.geojson"
    if lines_file.exists():
        try:
            gdf = gpd.read_file(lines_file)
            if len(gdf) > 0:
                sample_geom = gdf.iloc[0].geometry
                if hasattr(sample_geom, 'coords'):
                    coords = list(sample_geom.coords)
                    print(f"\nSample LineString coordinates:")
                    print(f"  Start point: [{coords[0][0]:.8f}, {coords[0][1]:.8f}]")
                    print(f"  End point: [{coords[-1][0]:.8f}, {coords[-1][1]:.8f}]")
                    print(f"  Total vertices: {len(coords)}")
        except Exception as e:
            print(f"Error reading line sample: {str(e)}")

def main():
    """Main function"""
    create_combined_geojson()
    show_sample_coordinates()
    
    print(f"\nFILES READY FOR GEOJSON.IO:")
    print("=" * 50)
    print("All files in geojson_output_wgs84/ are now in WGS84 format:")
    print("  - polygons.geojson")
    print("  - lines.geojson") 
    print("  - points.geojson")
    print("  - combined_features.geojson")
    print()
    print("These should work perfectly with geojson.io and other web mapping tools!")

if __name__ == "__main__":
    main()
