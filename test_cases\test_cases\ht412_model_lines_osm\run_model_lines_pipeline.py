#!/usr/bin/env python3
"""
HT412 Model Line Strings with OSM Attributes Pipeline
Extracts line strings from HT412 LiDAR using models and enriches with OSM attributes
"""

import sys
import time
import subprocess
from pathlib import Path
import json

def run_script(script_path, description):
    """Run a Python script and handle errors"""
    print(f"\n{'='*60}")
    print(f"[RUNNING] {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run([
            sys.executable, str(script_path)
        ], capture_output=True, text=True, check=True)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        print(f"[SUCCESS] {description} completed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] {description} failed with return code {e.returncode}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False
    except Exception as e:
        print(f"[ERROR] Error running {description}: {str(e)}")
        return False

def check_dependencies():
    """Check if required dependencies are available"""
    print("[INFO] Checking dependencies...")
    
    # Core required packages
    required_packages = [
        'laspy', 'numpy', 'pandas', 'geopandas', 
        'shapely', 'sklearn', 'matplotlib', 'pyproj', 'overpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except (ImportError, FileNotFoundError):
            missing_packages.append(package)
    
    if missing_packages:
        print(f"[ERROR] Missing dependencies: {', '.join(missing_packages)}")
        print("\nTo install missing Python packages:")
        python_packages = [p for p in missing_packages if not p.startswith('pdal')]
        if python_packages:
            print(f"conda run -n inti pip install {' '.join(python_packages)}")
        
        return False
    
    print("[SUCCESS] All dependencies are available!")
    return True

def check_input_file():
    """Check if input LAS file exists"""
    input_file = Path("D:/Integrate/HT412_1738935654_3217135_1422974157258029_1422974185645127_Clip.las")
    
    if not input_file.exists():
        print(f"[ERROR] Input LAS file not found: {input_file}")
        print("Please ensure the file exists at the specified location.")
        return False
    
    file_size = input_file.stat().st_size / (1024 * 1024)  # MB
    print(f"[SUCCESS] Input file found: {input_file.name} ({file_size:.2f} MB)")
    return True

def main():
    """Main pipeline execution"""
    start_time = time.time()
    
    print("HT412 Model Line Strings with OSM Attributes Pipeline")
    print("=" * 60)
    print("This pipeline will:")
    print("1. Verify intensity range 20-45 in HT412 LiDAR data")
    print("2. Extract line strings from LiDAR using ML models")
    print("3. Query OpenStreetMap for road/highway data")
    print("4. Associate OSM attributes with model-generated lines")
    print("5. Transform coordinates to EPSG:25832")
    print("6. Generate enriched GeoJSON output")
    print("7. Create WGS84 version for geojson.io")
    print("=" * 60)
    
    # Get script directory
    script_dir = Path(__file__).parent
    
    # Check dependencies
    if not check_dependencies():
        print("\n[FAILED] Pipeline aborted due to missing dependencies!")
        sys.exit(1)
    
    # Check input file
    if not check_input_file():
        print("\n[FAILED] Pipeline aborted due to missing input file!")
        sys.exit(1)
    
    # Pipeline steps
    steps = [
        (script_dir / "check_intensity.py", "Intensity Range Verification"),
        (script_dir / "model_line_extractor.py", "Model Line String Extraction"),
        (script_dir / "osm_attribute_enricher.py", "OSM Attribute Enrichment"),
        (script_dir / "geojson_generator.py", "Final GeoJSON Generation"),
        (script_dir / "transform_to_wgs84.py", "WGS84 Transformation")
    ]
    
    # Execute pipeline steps
    for step_script, step_description in steps:
        if not step_script.exists():
            print(f"[ERROR] Script not found: {step_script}")
            sys.exit(1)
        
        success = run_script(step_script, step_description)
        
        if not success:
            print(f"\n[FAILED] Pipeline failed at step: {step_description}")
            sys.exit(1)
        
        # Small delay between steps
        time.sleep(1)
    
    # Pipeline completed
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n{'='*60}")
    print("[SUCCESS] HT412 MODEL LINES WITH OSM ATTRIBUTES PIPELINE COMPLETED!")
    print(f"{'='*60}")
    print(f"[INFO] Total execution time: {duration:.2f} seconds")
    print(f"[INFO] Output files are in: {script_dir}")
    print("\nGenerated outputs:")
    print("  - model_lines_with_osm_final_wgs84.geojson - Main output for geojson.io")
    print("  - model_lines_with_osm_final.geojson - Main output (UTM32N)")
    print("  - model_lines_only.geojson - Model-generated lines without OSM")
    print("  - osm_raw_data.geojson - Raw OSM data for reference")
    print("  - processing_statistics.json - Detailed statistics")
    print("  - processing_summary.txt - Human-readable summary")
    
    # Check output files
    output_files = [
        "model_lines_with_osm_final_wgs84.geojson",
        "model_lines_with_osm_final.geojson",
        "model_lines_only.geojson",
        "osm_raw_data.geojson"
    ]
    
    print(f"\n[INFO] Generated files:")
    for filename in output_files:
        file_path = script_dir / filename
        if file_path.exists():
            file_size = file_path.stat().st_size / 1024  # KB
            print(f"   - {filename} ({file_size:.1f} KB)")
        else:
            print(f"   - {filename} (NOT FOUND)")

if __name__ == "__main__":
    main()
