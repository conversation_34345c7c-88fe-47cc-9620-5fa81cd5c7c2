{"total_lines": 100, "lines_with_osm_attributes": "100", "total_length_meters": 2648.49, "average_length_meters": 26.48, "osm_attribute_coverage": 100.0, "highway_types": {"motorway": 97, "secondary": 3}, "osm_attributes_found": ["osm_distance", "osm_is_sidepath:of:name", "osm_oneway:bicycle", "osm_source:maxspeed", "osm_maxspeed", "osm_surface", "osm_osm_id", "osm_public_transport", "osm_foot", "osm_lit", "osm_oneway", "osm_name:<PERSON><PERSON>", "osm_old_ref", "osm_segregated", "osm_destination:colour", "osm_highway", "osm_is_sidepath:of", "osm_motorcar", "osm_bicycle", "osm_sidewalk:left", "osm_lane_markings", "osm_name:vbb", "osm_tactile_paving", "osm_footway", "osm_destination:colour:backward", "osm_ref", "osm_motorcycle", "osm_reg_name", "osm_bridge", "osm_change:lanes", "osm_trail_visibility", "osm_destination:lanes", "osm_sidewalk", "osm_motor_vehicle", "osm_wheelchair", "osm_traffic_sign", "osm_bus", "osm_layer", "osm_destination:ref:lanes", "osm_name", "osm_destination:backward", "osm_turn:lanes", "osm_sidewalk:both", "osm_bin", "osm_maxheight", "osm_smoothness", "osm_placement", "osm_maxspeed:backward", "osm_lanes:forward", "osm_covered", "osm_passenger_information_display", "osm_destination:ref", "osm_note", "osm_destination", "osm_cycleway:left", "osm_is_sidepath", "osm_service", "osm_lanes:backward", "osm_width", "osm_vehicle", "osm_shelter", "osm_maxspeed:forward", "osm_snowmobile", "osm_cycleway:right", "osm_turn:lanes:backward", "osm_sidewalk:right", "osm_osm_type", "osm_tracktype", "osm_access", "osm_cycleway:both", "osm_destination:arrow:lanes", "osm_hgv", "osm_destination:symbol", "osm_ski", "osm_bench", "osm_lanes"]}