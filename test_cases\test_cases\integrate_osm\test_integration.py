#!/usr/bin/env python3
"""
Integration Test Script
Tests the integrated LiDAR pipeline components
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing module imports...")
    
    modules = [
        ('laspy', 'LAS file processing'),
        ('numpy', 'Numerical computations'),
        ('pandas', 'Data manipulation'),
        ('geopandas', 'Geospatial data processing'),
        ('shapely', 'Geometric operations'),
        ('sklearn', 'Machine learning'),
        ('pyproj', 'Coordinate transformations'),
        ('tqdm', 'Progress bars')
    ]
    
    all_good = True
    
    for module, description in modules:
        try:
            __import__(module)
            print(f"  ✓ {module} - {description}")
        except ImportError:
            print(f"  ✗ {module} - {description} (MISSING)")
            all_good = False
    
    return all_good

def test_integrated_modules():
    """Test if our integrated modules can be imported"""
    print("\nTesting integrated modules...")
    
    modules = [
        ('complete_lidar_pipeline', 'Main LiDAR processor'),
        ('pdal_processor', 'PDAL wrapper'),
        ('coordinate_transformer', 'Coordinate transformation'),
        ('run_integrated_pipeline', 'Pipeline runner')
    ]
    
    all_good = True
    
    for module, description in modules:
        try:
            __import__(module)
            print(f"  ✓ {module} - {description}")
        except ImportError as e:
            print(f"  ✗ {module} - {description} (ERROR: {e})")
            all_good = False
    
    return all_good

def test_pdal_availability():
    """Test if PDAL command is available"""
    print("\nTesting PDAL availability...")
    
    import subprocess
    
    try:
        result = subprocess.run(['pdal', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"  ✓ PDAL available - {version}")
            return True
        else:
            print(f"  ✗ PDAL command failed with return code {result.returncode}")
            return False
    except FileNotFoundError:
        print("  ✗ PDAL command not found")
        print("    Install with: conda install -c conda-forge pdal")
        return False
    except subprocess.TimeoutExpired:
        print("  ✗ PDAL command timed out")
        return False
    except Exception as e:
        print(f"  ✗ Error testing PDAL: {str(e)}")
        return False

def test_sample_data():
    """Test if sample data is available"""
    print("\nTesting sample data availability...")
    
    # Look for LAS files in parent directories
    current_dir = Path(__file__).parent
    search_dirs = [
        current_dir.parent,  # test_cases
        current_dir.parent.parent,  # review_sample
    ]
    
    las_files = []
    for search_dir in search_dirs:
        if search_dir.exists():
            las_files.extend(list(search_dir.glob("*.las")))
            las_files.extend(list(search_dir.glob("**/*.las")))
    
    if las_files:
        print(f"  ✓ Found {len(las_files)} LAS file(s):")
        for las_file in las_files[:3]:  # Show first 3
            file_size = las_file.stat().st_size / (1024 * 1024)  # MB
            print(f"    - {las_file} ({file_size:.1f} MB)")
        if len(las_files) > 3:
            print(f"    ... and {len(las_files) - 3} more")
        return True
    else:
        print("  ✗ No LAS files found")
        print("    Place a LAS file in the parent directory for testing")
        return False

def test_coordinate_systems():
    """Test coordinate system support"""
    print("\nTesting coordinate system support...")
    
    try:
        import pyproj
        
        # Test common CRS
        test_crs = [
            ('EPSG:4326', 'WGS84'),
            ('EPSG:25832', 'UTM 32N'),
            ('EPSG:25834', 'UTM 34N'),
            ('EPSG:3857', 'Web Mercator')
        ]
        
        all_good = True
        
        for crs_code, description in test_crs:
            try:
                crs = pyproj.CRS(crs_code)
                print(f"  ✓ {crs_code} - {description}")
            except Exception as e:
                print(f"  ✗ {crs_code} - {description} (ERROR: {e})")
                all_good = False
        
        # Test transformation
        try:
            transformer = pyproj.Transformer.from_crs("EPSG:25832", "EPSG:4326", always_xy=True)
            lon, lat = transformer.transform(500000, 5000000)
            print(f"  ✓ Coordinate transformation test: UTM32N → WGS84")
            print(f"    (500000, 5000000) → ({lon:.6f}, {lat:.6f})")
        except Exception as e:
            print(f"  ✗ Coordinate transformation test failed: {e}")
            all_good = False
        
        return all_good
        
    except ImportError:
        print("  ✗ pyproj not available")
        return False

def test_file_permissions():
    """Test file write permissions"""
    print("\nTesting file permissions...")
    
    current_dir = Path(__file__).parent
    
    test_dirs = [
        current_dir,
        current_dir / "test_output"
    ]
    
    all_good = True
    
    for test_dir in test_dirs:
        try:
            # Create directory if it doesn't exist
            test_dir.mkdir(exist_ok=True)
            
            # Try to write a test file
            test_file = test_dir / "test_write.tmp"
            test_file.write_text("test")
            test_file.unlink()  # Delete test file
            
            print(f"  ✓ {test_dir} - writable")
            
        except Exception as e:
            print(f"  ✗ {test_dir} - not writable ({str(e)})")
            all_good = False
    
    return all_good

def show_usage_examples():
    """Show usage examples"""
    print("\nUSAGE EXAMPLES:")
    print("-" * 50)
    
    print("1. Complete pipeline with PDAL:")
    print("   python run_integrated_pipeline.py -i input.las -o output/")
    print()
    
    print("2. Skip PDAL preprocessing:")
    print("   python run_integrated_pipeline.py -i filtered.las -o output/ --no-pdal")
    print()
    
    print("3. Custom parameters:")
    print("   python run_integrated_pipeline.py -i input.las -o output/ \\")
    print("     --chunk-size 1000 --intensity-min 50 --intensity-max 200")
    print()
    
    print("4. Individual components:")
    print("   python pdal_processor.py -i input.las -o filtered.las")
    print("   python complete_lidar_pipeline.py -i filtered.las -o features/")
    print("   python coordinate_transformer.py -i input.geojson -o output.geojson --source 25832 --target 4326")

def main():
    """Run all tests"""
    print("INTEGRATED LIDAR PIPELINE - INTEGRATION TEST")
    print("=" * 60)
    
    tests = [
        ("Python Dependencies", test_imports),
        ("Integrated Modules", test_integrated_modules),
        ("PDAL Availability", test_pdal_availability),
        ("Sample Data", test_sample_data),
        ("Coordinate Systems", test_coordinate_systems),
        ("File Permissions", test_file_permissions)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ✗ Test failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Integrated pipeline is ready to use.")
        show_usage_examples()
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix issues before using pipeline.")
        
        print("\nCommon fixes:")
        print("- Install missing packages: pip install -r requirements.txt")
        print("- Install PDAL: conda install -c conda-forge pdal")
        print("- Ensure LAS files are available for testing")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
