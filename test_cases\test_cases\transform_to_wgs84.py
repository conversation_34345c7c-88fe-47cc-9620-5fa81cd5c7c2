#!/usr/bin/env python3
"""
Enhanced GeoJSON Coordinate Transformer
Transform coordinates from any EPSG to WGS84 (EPSG:4326) with support for all geometry types
"""

import json
import pyproj
from pyproj import Transformer
from tqdm import tqdm
import numpy as np
from pathlib import Path

def transform_coordinates(coords, transformer, geometry_type):
    """
    Recursively transform coordinates based on geometry type.
    
    Args:
        coords: Coordinate array (structure depends on geometry type)
        transformer: pyproj Transformer object
        geometry_type: Type of geometry (Point, LineString, Polygon, etc.)
    
    Returns:
        Transformed coordinates with same structure
    """
    if geometry_type == 'Point':
        # Point: [x, y] or [x, y, z]
        x, y = coords[0], coords[1]
        lon, lat = transformer.transform(x, y)
        return [round(lon, 8), round(lat, 8)]
    
    elif geometry_type == 'LineString':
        # LineString: [[x1, y1], [x2, y2], ...]
        transformed_coords = []
        for x, y in coords:
            lon, lat = transformer.transform(x, y)
            transformed_coords.append([round(lon, 8), round(lat, 8)])
        return transformed_coords
    
    elif geometry_type == 'Polygon':
        # Polygon: [[[x1, y1], [x2, y2], ...], [...]] (exterior + holes)
        transformed_rings = []
        for ring in coords:
            transformed_ring = []
            for x, y in ring:
                lon, lat = transformer.transform(x, y)
                transformed_ring.append([round(lon, 8), round(lat, 8)])
            transformed_rings.append(transformed_ring)
        return transformed_rings
    
    elif geometry_type in ['MultiPoint', 'MultiLineString', 'MultiPolygon']:
        # Multi-geometries: recursively transform each component
        base_type = geometry_type.replace('Multi', '')
        transformed_coords = []
        for component in coords:
            transformed_component = transform_coordinates(component, transformer, base_type)
            transformed_coords.append(transformed_component)
        return transformed_coords
    
    else:
        # Fallback for unknown geometry types
        print(f"Warning: Unknown geometry type '{geometry_type}', skipping transformation")
        return coords

def transform_geojson_to_wgs84(input_geojson, output_geojson, source_epsg=25832):
    """
    Transform coordinates in a GeoJSON file from source EPSG to WGS84 (EPSG:4326).
    
    Args:
        input_geojson (str): Path to input GeoJSON file
        output_geojson (str): Path to save transformed GeoJSON file
        source_epsg (int): EPSG code of the source coordinate system
    """
    print(f"Transforming {input_geojson}")
    print(f"  From: EPSG:{source_epsg}")
    print(f"  To: WGS84 (EPSG:4326)")
    print(f"  Output: {output_geojson}")
    
    # Create transformer
    transformer = Transformer.from_crs(f"EPSG:{source_epsg}", "EPSG:4326", always_xy=True)
    
    # Read input GeoJSON
    try:
        with open(input_geojson, 'r', encoding='utf-8') as f:
            geojson = json.load(f)
    except FileNotFoundError:
        print(f"Error: Input file '{input_geojson}' not found!")
        return False
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in '{input_geojson}': {e}")
        return False
    
    # Validate GeoJSON structure
    if 'features' not in geojson:
        print("Error: Invalid GeoJSON - missing 'features' array")
        return False
    
    # Statistics
    geometry_types = {}
    transformed_features = 0
    
    # Process each feature
    for feature in tqdm(geojson['features'], desc="Transforming features"):
        if 'geometry' not in feature or feature['geometry'] is None:
            continue
        
        geometry = feature['geometry']
        geom_type = geometry['type']
        
        # Count geometry types
        geometry_types[geom_type] = geometry_types.get(geom_type, 0) + 1
        
        # Transform coordinates
        try:
            transformed_coords = transform_coordinates(
                geometry['coordinates'], 
                transformer, 
                geom_type
            )
            feature['geometry']['coordinates'] = transformed_coords
            transformed_features += 1
        except Exception as e:
            print(f"Warning: Failed to transform feature with geometry type '{geom_type}': {e}")
    
    # Update CRS information
    geojson['crs'] = {
        "type": "name",
        "properties": {
            "name": "urn:ogc:def:crs:OGC:1.3:CRS84"
        }
    }
    
    # Save transformed GeoJSON
    try:
        with open(output_geojson, 'w', encoding='utf-8') as f:
            json.dump(geojson, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"Error: Failed to save output file '{output_geojson}': {e}")
        return False
    
    # Print summary
    print(f"\nTransformation completed successfully!")
    print(f"  Features processed: {len(geojson['features'])}")
    print(f"  Features transformed: {transformed_features}")
    print(f"  Geometry types found:")
    for geom_type, count in geometry_types.items():
        print(f"    {geom_type}: {count}")
    
    # Calculate file sizes
    input_size = Path(input_geojson).stat().st_size / 1024  # KB
    output_size = Path(output_geojson).stat().st_size / 1024  # KB
    print(f"  Input file size: {input_size:.1f} KB")
    print(f"  Output file size: {output_size:.1f} KB")
    
    return True

def transform_all_files_in_directory(input_dir, output_dir, source_epsg=25832):
    """
    Transform all GeoJSON files in a directory.
    
    Args:
        input_dir (str): Directory containing input GeoJSON files
        output_dir (str): Directory to save transformed files
        source_epsg (int): Source EPSG code
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    if not input_path.exists():
        print(f"Error: Input directory '{input_dir}' does not exist!")
        return
    
    # Create output directory
    output_path.mkdir(exist_ok=True)
    
    # Find all GeoJSON files
    geojson_files = list(input_path.glob("*.geojson"))
    
    if not geojson_files:
        print(f"No GeoJSON files found in '{input_dir}'")
        return
    
    print(f"Found {len(geojson_files)} GeoJSON files to transform")
    print("=" * 60)
    
    successful = 0
    failed = 0
    
    for geojson_file in geojson_files:
        output_file = output_path / geojson_file.name
        
        if transform_geojson_to_wgs84(str(geojson_file), str(output_file), source_epsg):
            successful += 1
        else:
            failed += 1
        
        print("-" * 40)
    
    print(f"\nBatch transformation summary:")
    print(f"  Successful: {successful}")
    print(f"  Failed: {failed}")
    print(f"  Output directory: {output_path}")

def main():
    """Main function with command line interface"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='Transform GeoJSON coordinates to WGS84 (EPSG:4326)',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Transform single file
  python transform_to_wgs84.py -i input.geojson -o output.geojson
  
  # Transform all files in directory
  python transform_to_wgs84.py --batch -i geojson_output_25832/ -o geojson_output_wgs84/
  
  # Transform from different source CRS
  python transform_to_wgs84.py -i input.geojson -o output.geojson --source-epsg 25834
        """
    )
    
    parser.add_argument('--input', '-i', required=True,
                        help='Input GeoJSON file path or directory (for batch mode)')
    parser.add_argument('--output', '-o', required=True,
                        help='Output GeoJSON file path or directory (for batch mode)')
    parser.add_argument('--source-epsg', type=int, default=25832,
                        help='EPSG code of the source coordinate system (default: 25832)')
    parser.add_argument('--batch', action='store_true',
                        help='Batch mode: transform all GeoJSON files in input directory')
    
    args = parser.parse_args()
    
    if args.batch:
        transform_all_files_in_directory(args.input, args.output, args.source_epsg)
    else:
        transform_geojson_to_wgs84(args.input, args.output, args.source_epsg)

if __name__ == "__main__":
    main()
