#!/usr/bin/env python3
"""
Check intensity values in LAS file to verify filtering
"""

import laspy
import numpy as np
from pathlib import Path

def check_intensity_values(las_file):
    """Check intensity statistics in LAS file"""
    print(f"[INFO] Checking intensity values in: {las_file}")
    
    if not Path(las_file).exists():
        print(f"[ERROR] File not found: {las_file}")
        return False
    
    # Read LAS file
    las = laspy.read(las_file)
    
    # Get basic info
    print(f"[INFO] Total points: {len(las.xyz):,}")
    
    # Check if intensity is available
    if hasattr(las, 'intensity'):
        intensity = las.intensity
        
        print(f"[INFO] Intensity statistics:")
        print(f"  Min intensity: {intensity.min()}")
        print(f"  Max intensity: {intensity.max()}")
        print(f"  Mean intensity: {intensity.mean():.2f}")
        print(f"  Std intensity: {intensity.std():.2f}")
        
        # Check filtering range
        in_range = np.sum((intensity >= 75) & (intensity <= 150))
        total_points = len(intensity)
        percentage = (in_range / total_points) * 100
        
        print(f"[INFO] Points with intensity 75-150: {in_range:,} ({percentage:.1f}%)")
        
        # Check if filtering was applied
        if intensity.min() >= 75 and intensity.max() <= 150:
            print(f"[SUCCESS] ✅ Intensity filtering (75-150) appears to be applied!")
            return True
        else:
            print(f"[WARNING] ⚠️  Intensity range suggests filtering may not be applied")
            print(f"[INFO] Expected range: 75-150, Actual range: {intensity.min()}-{intensity.max()}")
            return False
    else:
        print(f"[ERROR] No intensity data found in LAS file")
        return False

def main():
    """Main execution"""
    las_file = "D:/Integrate/FusedLidar_Lidar1_UTM32N_filtered.las"
    
    print("LAS File Intensity Check")
    print("=" * 40)
    
    check_intensity_values(las_file)

if __name__ == "__main__":
    main()
