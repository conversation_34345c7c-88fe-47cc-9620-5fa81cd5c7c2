#!/usr/bin/env python3
"""
Complete LiDAR to GeoJSON Pipeline
Integrated pipeline for processing LiDAR data and generating GeoJSON features

This script combines all the functionality from the test_cases pipeline into a single
integrated solution for converting LiDAR data to GeoJSON format.

Features:
- Chunked processing for large files
- RANSAC ground plane filtering
- DBSCAN clustering for feature detection
- Polygon and line string extraction
- Multiple output coordinate systems
- Compatible with geojson.io and web mapping tools
"""

import os
import sys
import time
import json
import subprocess
from pathlib import Path
import laspy
import numpy as np
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point, LineString, Polygon, MultiPoint
from sklearn.cluster import DBSCAN
from sklearn.decomposition import PCA
from sklearn.linear_model import RANS<PERSON>Regressor, LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline
from typing import List, Dict, Any, Tuple
import math

class LiDARProcessor:
    """Complete LiDAR processing pipeline"""
    
    def __init__(self, las_file: str, chunk_size_meters: float = 500.0, output_dir: str = "output"):
        """
        Initialize the LiDAR processor
        
        Args:
            las_file (str): Path to the LAS file
            chunk_size_meters (float): Size of each chunk in meters
            output_dir (str): Output directory for results
        """
        self.las_file = las_file
        self.chunk_size = chunk_size_meters
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Processing data
        self.las_data = None
        self.bounds = None
        self.chunks_info = []
        
        # Results
        self.all_polygons = []
        self.all_lines = []
        self.all_points = []
    
    def load_las_file(self) -> bool:
        """Load the LAS file and get basic information"""
        try:
            print(f"Loading LAS file: {self.las_file}")
            self.las_data = laspy.read(self.las_file)
            
            # Get bounds
            self.bounds = {
                'min_x': self.las_data.header.x_min,
                'max_x': self.las_data.header.x_max,
                'min_y': self.las_data.header.y_min,
                'max_y': self.las_data.header.y_max,
                'min_z': self.las_data.header.z_min,
                'max_z': self.las_data.header.z_max
            }
            
            print(f"LAS File Information:")
            print(f"   Points: {len(self.las_data.points):,}")
            print(f"   Bounds X: {self.bounds['min_x']:.2f} to {self.bounds['max_x']:.2f}")
            print(f"   Bounds Y: {self.bounds['min_y']:.2f} to {self.bounds['max_y']:.2f}")
            print(f"   Bounds Z: {self.bounds['min_z']:.2f} to {self.bounds['max_z']:.2f}")
            print(f"   CRS: {self.las_data.header.parse_crs()}")
            
            return True
            
        except Exception as e:
            print(f"Error loading LAS file: {str(e)}")
            return False
    
    def calculate_chunks(self) -> List[Dict[str, Any]]:
        """Calculate chunk boundaries"""
        if not self.bounds:
            raise ValueError("LAS file not loaded")
        
        # Calculate number of chunks in each direction
        x_range = self.bounds['max_x'] - self.bounds['min_x']
        y_range = self.bounds['max_y'] - self.bounds['min_y']
        
        x_chunks = math.ceil(x_range / self.chunk_size)
        y_chunks = math.ceil(y_range / self.chunk_size)
        
        print(f"Calculating chunks:")
        print(f"   X range: {x_range:.2f}m ({x_chunks} chunks)")
        print(f"   Y range: {y_range:.2f}m ({y_chunks} chunks)")
        print(f"   Total chunks: {x_chunks * y_chunks}")
        
        chunks = []
        chunk_id = 0
        
        for i in range(x_chunks):
            for j in range(y_chunks):
                x_min = self.bounds['min_x'] + i * self.chunk_size
                x_max = min(x_min + self.chunk_size, self.bounds['max_x'])
                y_min = self.bounds['min_y'] + j * self.chunk_size
                y_max = min(y_min + self.chunk_size, self.bounds['max_y'])
                
                chunk = {
                    'id': chunk_id,
                    'x_index': i,
                    'y_index': j,
                    'bounds': {
                        'min_x': x_min,
                        'max_x': x_max,
                        'min_y': y_min,
                        'max_y': y_max
                    }
                }
                chunks.append(chunk)
                chunk_id += 1
        
        self.chunks_info = chunks
        return chunks
    
    def extract_chunk_data(self, chunk: Dict[str, Any]) -> np.ndarray:
        """Extract point data for a specific chunk"""
        bounds = chunk['bounds']
        
        # Create mask for points within chunk bounds
        x_mask = (self.las_data.x >= bounds['min_x']) & (self.las_data.x < bounds['max_x'])
        y_mask = (self.las_data.y >= bounds['min_y']) & (self.las_data.y < bounds['max_y'])
        chunk_mask = x_mask & y_mask
        
        if not np.any(chunk_mask):
            return np.array([]).reshape(0, 7)  # Empty array with correct shape
        
        # Extract point attributes
        x = self.las_data.x[chunk_mask]
        y = self.las_data.y[chunk_mask]
        z = self.las_data.z[chunk_mask]
        intensity = self.las_data.intensity[chunk_mask]
        return_num = self.las_data.return_number[chunk_mask]
        num_returns = self.las_data.number_of_returns[chunk_mask]
        classification = self.las_data.classification[chunk_mask]
        
        # Stack into array
        points = np.column_stack([x, y, z, intensity, return_num, num_returns, classification])
        
        return points
    
    def process_chunk_for_features(self, chunk_data: np.ndarray, chunk_id: int) -> Tuple[List, List, List]:
        """Process a single chunk to extract features"""
        if len(chunk_data) == 0:
            return [], [], []
        
        # Convert to DataFrame for easier processing
        df = pd.DataFrame(chunk_data, columns=['X', 'Y', 'Z', 'Intensity', 'ReturnNum', 'NumReturns', 'Classification'])
        
        # Filter by intensity (75-150 range as per PDAL pipeline)
        df = df[(df['Intensity'] >= 75) & (df['Intensity'] <= 150)].copy()
        
        if len(df) < 10:  # Not enough points for meaningful clustering
            return [], [], []
        
        # Ground plane filtering using RANSAC
        XY = df[['X', 'Y']].values
        Z = df['Z'].values
        
        try:
            plane_model = make_pipeline(
                PolynomialFeatures(degree=1, include_bias=True),
                RANSACRegressor(
                    estimator=LinearRegression(),
                    residual_threshold=0.1,  # 10cm threshold
                    random_state=42
                )
            )
            plane_model.fit(XY, Z)
            Z_pred = plane_model.predict(XY)
            
            # Keep points close to ground plane
            df['residual'] = np.abs(Z - Z_pred)
            df = df[df['residual'] <= 0.1].copy()
            
        except Exception as e:
            print(f"   Warning: RANSAC failed for chunk {chunk_id}: {str(e)}")
            # Continue without ground filtering
        
        if len(df) < 10:
            return [], [], []
        
        # DBSCAN clustering
        coords = df[['X', 'Y']].values
        
        # Adaptive eps based on point density
        area = (coords[:, 0].max() - coords[:, 0].min()) * (coords[:, 1].max() - coords[:, 1].min())
        density = len(coords) / max(area, 1)
        eps = max(0.5, min(2.0, 10.0 / np.sqrt(density)))  # Adaptive eps
        
        db = DBSCAN(eps=eps, min_samples=5).fit(coords)
        df['cluster'] = db.labels_
        
        chunk_polygons = []
        chunk_lines = []
        chunk_points = []
        
        # Process each cluster
        for cluster_id in sorted(df['cluster'].unique()):
            if cluster_id < 0:  # Skip noise points
                continue
            
            cluster_points = df[df['cluster'] == cluster_id][['X', 'Y']].values
            
            if len(cluster_points) < 5:
                continue
            
            try:
                # Create convex hull (polygon)
                multipoint = MultiPoint(cluster_points)
                hull = multipoint.convex_hull
                
                if hull.geom_type == 'Polygon' and hull.area > 1.0:  # Minimum area threshold
                    chunk_polygons.append({
                        'geometry': hull,
                        'properties': {
                            'chunk_id': chunk_id,
                            'cluster_id': cluster_id,
                            'point_count': len(cluster_points),
                            'area': hull.area
                        }
                    })
                
                # Create centerline using PCA
                if len(cluster_points) >= 10:
                    pca = PCA(n_components=2).fit(cluster_points)
                    center = pca.mean_
                    direction = pca.components_[0]
                    
                    # Extend line along principal direction
                    span = max(hull.bounds[2] - hull.bounds[0], hull.bounds[3] - hull.bounds[1]) * 1.5
                    
                    line_start = center - direction * span / 2
                    line_end = center + direction * span / 2
                    
                    line = LineString([line_start, line_end])
                    
                    # Intersect with hull to get actual centerline
                    centerline = hull.intersection(line)
                    
                    if centerline.geom_type == 'LineString' and centerline.length > 2.0:
                        chunk_lines.append({
                            'geometry': centerline,
                            'properties': {
                                'chunk_id': chunk_id,
                                'cluster_id': cluster_id,
                                'length': centerline.length,
                                'point_count': len(cluster_points)
                            }
                        })
                    elif centerline.geom_type == 'MultiLineString':
                        # Take the longest segment
                        longest = max(centerline.geoms, key=lambda x: x.length)
                        if longest.length > 2.0:
                            chunk_lines.append({
                                'geometry': longest,
                                'properties': {
                                    'chunk_id': chunk_id,
                                    'cluster_id': cluster_id,
                                    'length': longest.length,
                                    'point_count': len(cluster_points)
                                }
                            })
                
            except Exception as e:
                print(f"   Warning: Error processing cluster {cluster_id} in chunk {chunk_id}: {str(e)}")
                continue
        
        # Sample representative points
        if len(df) > 0:
            # Sample every 10th point or max 100 points per chunk
            sample_size = min(100, max(1, len(df) // 10))
            sampled_df = df.sample(n=sample_size, random_state=42)
            
            for _, row in sampled_df.iterrows():
                chunk_points.append({
                    'geometry': Point(row['X'], row['Y']),
                    'properties': {
                        'chunk_id': chunk_id,
                        'intensity': row['Intensity'],
                        'z': row['Z'],
                        'classification': row['Classification']
                    }
                })
        
        return chunk_polygons, chunk_lines, chunk_points
    
    def process_all_chunks(self) -> bool:
        """Process all chunks to extract features"""
        if not self.chunks_info:
            self.calculate_chunks()
        
        print(f"Processing {len(self.chunks_info)} chunks for feature extraction...")
        
        processed_chunks = 0
        
        for i, chunk in enumerate(self.chunks_info):
            print(f"   Processing chunk {i+1}/{len(self.chunks_info)} (ID: {chunk['id']})", end="")
            
            try:
                # Extract chunk data
                chunk_data = self.extract_chunk_data(chunk)
                
                if len(chunk_data) == 0:
                    print(" - Empty chunk, skipping")
                    continue
                
                # Extract features
                polygons, lines, points = self.process_chunk_for_features(chunk_data, chunk['id'])
                
                # Add to collections
                self.all_polygons.extend(polygons)
                self.all_lines.extend(lines)
                self.all_points.extend(points)
                
                print(f" - {len(polygons)} polygons, {len(lines)} lines, {len(points)} points")
                processed_chunks += 1
                
            except Exception as e:
                print(f" - Error: {str(e)}")
                continue
        
        print(f"Feature extraction complete!")
        print(f"   Processed chunks: {processed_chunks}")
        print(f"   Total polygons: {len(self.all_polygons)}")
        print(f"   Total lines: {len(self.all_lines)}")
        print(f"   Total points: {len(self.all_points)}")
        
        return True
    
    def save_geojson(self, output_crs: str = "EPSG:4326") -> bool:
        """Save extracted features as GeoJSON files"""
        try:
            # Determine source CRS from LAS file
            source_crs = "EPSG:25832"  # UTM32N as specified in pipeline
            
            # Create GeoDataFrames and save individual files
            if self.all_polygons:
                polygons_gdf = gpd.GeoDataFrame([
                    {**item['properties'], 'geometry': item['geometry']} 
                    for item in self.all_polygons
                ], crs=source_crs)
                
                polygons_gdf = polygons_gdf.to_crs(output_crs)
                polygons_file = self.output_dir / "polygons.geojson"
                polygons_gdf.to_file(polygons_file, driver="GeoJSON")
                print(f"Saved polygons: {polygons_file}")
            
            if self.all_lines:
                lines_gdf = gpd.GeoDataFrame([
                    {**item['properties'], 'geometry': item['geometry']} 
                    for item in self.all_lines
                ], crs=source_crs)
                
                lines_gdf = lines_gdf.to_crs(output_crs)
                lines_file = self.output_dir / "lines.geojson"
                lines_gdf.to_file(lines_file, driver="GeoJSON")
                print(f"Saved lines: {lines_file}")
            
            if self.all_points:
                points_gdf = gpd.GeoDataFrame([
                    {**item['properties'], 'geometry': item['geometry']} 
                    for item in self.all_points
                ], crs=source_crs)
                
                points_gdf = points_gdf.to_crs(output_crs)
                points_file = self.output_dir / "points.geojson"
                points_gdf.to_file(points_file, driver="GeoJSON")
                print(f"Saved points: {points_file}")
            
            # Create combined file
            combined_features = []
            
            for item in self.all_polygons:
                properties = {}
                for key, value in item['properties'].items():
                    if hasattr(value, 'item'):  # numpy scalar
                        properties[key] = value.item()
                    else:
                        properties[key] = value
                
                feature = {
                    "type": "Feature",
                    "geometry": item['geometry'].__geo_interface__,
                    "properties": {**properties, "feature_type": "polygon"}
                }
                combined_features.append(feature)
            
            for item in self.all_lines:
                properties = {}
                for key, value in item['properties'].items():
                    if hasattr(value, 'item'):  # numpy scalar
                        properties[key] = value.item()
                    else:
                        properties[key] = value
                
                feature = {
                    "type": "Feature", 
                    "geometry": item['geometry'].__geo_interface__,
                    "properties": {**properties, "feature_type": "line"}
                }
                combined_features.append(feature)
            
            combined_geojson = {
                "type": "FeatureCollection",
                "crs": {"type": "name", "properties": {"name": output_crs}},
                "features": combined_features
            }
            
            combined_file = self.output_dir / "combined_features.geojson"
            with open(combined_file, 'w') as f:
                json.dump(combined_geojson, f, indent=2)
            
            print(f"Saved combined features: {combined_file}")
            
            return True
            
        except Exception as e:
            print(f"Error saving GeoJSON: {str(e)}")
            return False
    
    def run_complete_pipeline(self, output_crs: str = "EPSG:4326") -> bool:
        """Run the complete processing pipeline"""
        print("Complete LiDAR Processing Pipeline")
        print("=" * 50)
        
        start_time = time.time()
        
        # Step 1: Load LAS file
        if not self.load_las_file():
            return False
        
        # Step 2: Process chunks and extract features
        if not self.process_all_chunks():
            return False
        
        # Step 3: Save GeoJSON files
        if not self.save_geojson(output_crs):
            return False
        
        # Summary
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\nPipeline completed successfully!")
        print(f"Total execution time: {duration:.2f} seconds")
        print(f"Output directory: {self.output_dir}")
        
        return True

def main():
    """Main function with command line interface"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Complete LiDAR to GeoJSON Pipeline')
    parser.add_argument('--input', '-i', required=True, help='Input LAS file path')
    parser.add_argument('--output', '-o', default='lidar_output', help='Output directory')
    parser.add_argument('--chunk-size', type=float, default=500.0, help='Chunk size in meters')
    parser.add_argument('--output-crs', default='EPSG:4326', help='Output coordinate system')
    
    args = parser.parse_args()
    
    # Initialize and run processor
    processor = LiDARProcessor(args.input, args.chunk_size, args.output)
    success = processor.run_complete_pipeline(args.output_crs)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
