#!/usr/bin/env python3
"""
GeoJSON Generator
Creates final GeoJSON output with model lines enriched with OSM attributes
"""

import json
import pandas as pd
import geopandas as gpd
from pathlib import Path
from datetime import datetime

class GeoJSONGenerator:
    """Generate final GeoJSON outputs with proper formatting and metadata"""
    
    def __init__(self, output_dir):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
    def load_enriched_lines(self):
        """Load enriched lines with OSM attributes"""
        enriched_file = self.output_dir / "model_lines_with_osm.geojson"
        
        if not enriched_file.exists():
            raise FileNotFoundError(f"Enriched lines file not found: {enriched_file}")
        
        enriched_lines = gpd.read_file(enriched_file)
        print(f"[INFO] Loaded {len(enriched_lines)} enriched line strings")
        
        return enriched_lines
    
    def clean_properties(self, properties_dict):
        """Clean properties for JSON serialization"""
        cleaned = {}
        
        for key, value in properties_dict.items():
            if key == 'geometry':
                continue
                
            # Handle different data types
            if pd.isna(value) or value == '' or value is None:
                continue  # Skip empty values
            elif hasattr(value, 'item'):  # numpy scalar
                cleaned[key] = value.item()
            elif isinstance(value, (int, float, str, bool)):
                cleaned[key] = value
            else:
                cleaned[key] = str(value)
        
        return cleaned
    
    def create_feature_collection(self, gdf, feature_type="line"):
        """Create GeoJSON FeatureCollection from GeoDataFrame"""
        features = []
        
        for idx, row in gdf.iterrows():
            # Clean properties
            properties = self.clean_properties(row.to_dict())
            
            # Add metadata
            properties['feature_type'] = feature_type
            properties['feature_id'] = idx
            
            # Create feature
            feature = {
                "type": "Feature",
                "geometry": row.geometry.__geo_interface__,
                "properties": properties
            }
            
            features.append(feature)
        
        # Create FeatureCollection
        feature_collection = {
            "type": "FeatureCollection",
            "crs": {
                "type": "name",
                "properties": {
                    "name": "EPSG:25832"
                }
            },
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "total_features": len(features),
                "coordinate_system": "EPSG:25832 (UTM Zone 32N)",
                "description": "Model-generated line strings enriched with OpenStreetMap attributes"
            },
            "features": features
        }
        
        return feature_collection
    
    def generate_statistics(self, enriched_lines):
        """Generate statistics about the enriched data"""
        stats = {
            "total_lines": len(enriched_lines),
            "lines_with_osm_attributes": 0,
            "total_length_meters": 0,
            "average_length_meters": 0,
            "osm_attribute_coverage": 0,
            "highway_types": {},
            "osm_attributes_found": []
        }
        
        # Calculate basic statistics
        if len(enriched_lines) > 0:
            stats["total_length_meters"] = round(enriched_lines['length'].sum(), 2)
            stats["average_length_meters"] = round(enriched_lines['length'].mean(), 2)
            
            # Count lines with OSM attributes
            lines_with_osm = enriched_lines['osm_distance'].notna().sum()
            stats["lines_with_osm_attributes"] = lines_with_osm
            stats["osm_attribute_coverage"] = round(lines_with_osm / len(enriched_lines) * 100, 1)
            
            # Analyze highway types
            if 'osm_highway' in enriched_lines.columns:
                highway_counts = enriched_lines['osm_highway'].value_counts()
                stats["highway_types"] = highway_counts.to_dict()
            
            # Find all OSM attributes
            osm_columns = [col for col in enriched_lines.columns if col.startswith('osm_')]
            stats["osm_attributes_found"] = osm_columns
        
        return stats
    
    def save_statistics(self, stats):
        """Save statistics to JSON file"""
        stats_file = self.output_dir / "processing_statistics.json"
        
        with open(stats_file, 'w') as f:
            json.dump(stats, f, indent=2, default=str)
        
        print(f"[INFO] Saved statistics: {stats_file}")
        return stats_file
    
    def create_summary_report(self, stats):
        """Create a human-readable summary report"""
        report_lines = [
            "Model Lines with OSM Attributes - Processing Summary",
            "=" * 60,
            "",
            f"Total line strings extracted: {stats['total_lines']:,}",
            f"Lines with OSM attributes: {stats['lines_with_osm_attributes']:,} ({stats['osm_attribute_coverage']}%)",
            f"Total length: {stats['total_length_meters']:,.2f} meters",
            f"Average line length: {stats['average_length_meters']:.2f} meters",
            "",
            "Highway Types Found:",
            "-" * 20
        ]
        
        # Add highway types
        for highway_type, count in stats.get('highway_types', {}).items():
            if highway_type and highway_type != '':
                report_lines.append(f"  {highway_type}: {count}")
        
        report_lines.extend([
            "",
            "OSM Attributes Available:",
            "-" * 25
        ])
        
        # Add OSM attributes
        for attr in stats.get('osm_attributes_found', []):
            report_lines.append(f"  {attr}")
        
        report_lines.extend([
            "",
            f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Coordinate System: EPSG:25832 (UTM Zone 32N)"
        ])
        
        # Save report
        report_file = self.output_dir / "processing_summary.txt"
        with open(report_file, 'w') as f:
            f.write('\n'.join(report_lines))
        
        print(f"[INFO] Saved summary report: {report_file}")
        
        # Also print to console
        print("\n" + '\n'.join(report_lines))
        
        return report_file
    
    def validate_geojson(self, geojson_data):
        """Basic validation of GeoJSON structure"""
        required_keys = ['type', 'features']
        
        for key in required_keys:
            if key not in geojson_data:
                raise ValueError(f"Missing required key: {key}")
        
        if geojson_data['type'] != 'FeatureCollection':
            raise ValueError("Invalid type, expected 'FeatureCollection'")
        
        if not isinstance(geojson_data['features'], list):
            raise ValueError("Features must be a list")
        
        print(f"[INFO] GeoJSON validation passed: {len(geojson_data['features'])} features")
        return True
    
    def generate_final_outputs(self):
        """Generate all final outputs"""
        print("[INFO] Generating final GeoJSON outputs...")
        
        # Load enriched lines
        enriched_lines = self.load_enriched_lines()
        
        # Generate statistics
        stats = self.generate_statistics(enriched_lines)
        
        # Create main GeoJSON output
        feature_collection = self.create_feature_collection(enriched_lines, "enriched_line")
        
        # Validate GeoJSON
        self.validate_geojson(feature_collection)
        
        # Save main output
        main_output = self.output_dir / "model_lines_with_osm_final.geojson"
        with open(main_output, 'w') as f:
            json.dump(feature_collection, f, indent=2)
        
        print(f"[SUCCESS] Saved final GeoJSON: {main_output}")
        
        # Save statistics
        self.save_statistics(stats)
        
        # Create summary report
        self.create_summary_report(stats)
        
        return main_output, stats

def main():
    """Main execution function"""
    print("[INFO] Starting Final GeoJSON Generation")
    print("-" * 50)
    
    # Configuration
    output_dir = Path(__file__).parent
    
    # Initialize generator
    generator = GeoJSONGenerator(output_dir)
    
    try:
        # Generate final outputs
        main_output, stats = generator.generate_final_outputs()
        
        print(f"\n[SUCCESS] Final GeoJSON generation completed!")
        print(f"[INFO] Main output: {main_output}")
        print(f"[INFO] Processed {stats['total_lines']} line strings")
        print(f"[INFO] {stats['osm_attribute_coverage']}% have OSM attributes")
        
    except Exception as e:
        print(f"[ERROR] Final GeoJSON generation failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
