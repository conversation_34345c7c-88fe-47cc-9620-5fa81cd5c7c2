 #!/usr/bin/env python3
import argparse
import laspy
import pandas as pd
import numpy as np
from sklearn.cluster import DBSCAN
from sklearn.decomposition import PCA
from sklearn.linear_model import RANSACRegressor, LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline
from shapely.geometry import MultiPoint, LineString
import geopandas as gpd
import matplotlib.pyplot as plt

def main():
    p = argparse.ArgumentParser(
        description="LAS → intensity + RANSAC ground-plane filter → DBSCAN → exact hull-slice centerlines → debug & export"
    )
    p.add_argument('-i','--input', required=True, help="Input LAS file")
    p.add_argument('-t','--intensity-thresh', type=float, default=30.0,
                   help="Min intensity to keep a point")
    p.add_argument('--ground-threshold', type=float, default=0.05,
                   help="Max Z residual (m) to classify as ground after RANSAC")
    p.add_argument('--eps', type=float, default=0.5,
                   help="DBSCAN eps (metres)")
    p.add_argument('--min-samples', type=int, default=10,
                   help="DBSCAN min_samples")
    p.add_argument('-o','--output-prefix', default='lane_proj',
                   help="Prefix for all output files")
    p.add_argument('--target-crs', default="EPSG:4326",
                   help="Output CRS (e.g. WGS84 lat/lon)")
    args = p.parse_args()

    # 1) Read LAS → xyz + intensity
    las   = laspy.read(args.input)
    xyz   = las.xyz                      # (N×3)
    inten = np.array(las.intensity, float)

    # 2) Filter by intensity
    df = pd.DataFrame(xyz, columns=['X','Y','Z'])
    df['Intensity'] = inten
    df = df[df['Intensity'] >= args.intensity_thresh].copy()

    # 3) RANSAC ground-plane fit (Z = aX + bY + c)
    XY = df[['X','Y']].values
    Z  = df['Z'].values
    plane_model = make_pipeline(
        PolynomialFeatures(degree=1, include_bias=True),
        RANSACRegressor(
            estimator=LinearRegression(),
            residual_threshold=args.ground_threshold,
            random_state=0
        )
    )
    plane_model.fit(XY, Z)
    Z_pred = plane_model.predict(XY)

    # Keep only "ground" points
    df['residual'] = np.abs(Z - Z_pred)
    df = df[df['residual'] <= args.ground_threshold].copy()

    # Debug-plot #1: post-RANSAC ground cloud
    fig1, ax1 = plt.subplots(figsize=(8,8))
    ax1.scatter(df.X, df.Y, s=1, c='k')
    ax1.set_title(f"Ground Points (Intensity ≥ {args.intensity_thresh}, |res|≤{args.ground_threshold} m)")
    ax1.set_xlabel("X (m)"); ax1.set_ylabel("Y (m)"); ax1.axis('equal')
    fig1.tight_layout()
    fig1.savefig(f"{args.output_prefix}_ground_filtered.png", dpi=300)
    plt.save()

    # 4) DBSCAN clustering on XY
    coords = df[['X','Y']].values
    db     = DBSCAN(eps=args.eps, min_samples=args.min_samples).fit(coords)
    df['cluster'] = db.labels_

    # 5) Exact hull-slice centerlines
    polys, lines, ids = [], [], []
    fig2, ax2 = plt.subplots(figsize=(8,8))
    cmap2 = plt.get_cmap('tab20', df['cluster'].nunique())

    for cid in sorted(df['cluster'].unique()):
        if cid < 0:
            continue
        pts  = df[df['cluster']==cid][['X','Y']].values
        hull = MultiPoint(pts).convex_hull

        # PCA → principal direction
        pca  = PCA(n_components=2).fit(pts)
        ctr  = pca.mean_
        dir0 = pca.components_[0]

        # Extend a long line along dir0 through centroid
        span = max(hull.bounds[2]-hull.bounds[0],
                   hull.bounds[3]-hull.bounds[1]) * 2.0
        L = LineString([
            tuple(ctr - dir0*span),
            tuple(ctr + dir0*span)
        ])

        # Intersect hull → chord(s)
        chord = hull.intersection(L)
        if chord.geom_type == 'MultiLineString':
            chord = max(chord, key=lambda seg: seg.length)
        if chord.geom_type != 'LineString':
            continue

        ids.append(cid)
        polys.append(hull)
        lines.append(chord)

        # Debug-plot centerline
        xs, ys = chord.xy
        ax2.plot(xs, ys, color=cmap2(cid), linewidth=2, label=f'c{cid}')

    ax2.set_title("Exact Hull-Slice Centerlines on Ground Points")
    ax2.set_xlabel("X (m)"); ax2.set_ylabel("Y (m)"); ax2.axis('equal')
    ax2.legend(loc='upper right', fontsize='x-small', ncol=2)
    fig2.tight_layout()
    fig2.savefig(f"{args.output_prefix}_centerlines_on_ground.png", dpi=300)
    plt.close(fig2)

    # 6) Wrap into GeoDataFrames (using source CRS)
    header_crs = las.header.parse_crs()
    if header_crs is None:
        raise RuntimeError("LAS header has no CRS—please hardcode EPSG.")
    src_epsg = header_crs.to_epsg() or header_crs.to_string()

    gdf_pts  = gpd.GeoDataFrame(df, geometry=gpd.points_from_xy(df.X, df.Y), crs=src_epsg)
    gdf_poly = gpd.GeoDataFrame({'cluster': ids}, geometry=polys, crs=src_epsg)
    gdf_line = gpd.GeoDataFrame({'cluster': ids}, geometry=lines, crs=src_epsg)

    # 7) Reproject to target CRS & save GeoJSON
    gdf_pts  = gdf_pts .to_crs(args.target_crs)
    gdf_poly = gdf_poly.to_crs(args.target_crs)
    gdf_line = gdf_line.to_crs(args.target_crs)

    gdf_pts .to_file(f"{args.output_prefix}_points.geojson",      driver="GeoJSON")
    gdf_poly.to_file(f"{args.output_prefix}_polygons.geojson",   driver="GeoJSON")
    gdf_line.to_file(f"{args.output_prefix}_centerlines.geojson",driver="GeoJSON")

    print("→ Saved:")
    print(f"   • {args.output_prefix}_ground_filtered.png")
    print(f"   • {args.output_prefix}_centerlines_on_ground.png")
    print(f"   • {args.output_prefix}_points.geojson")
    print(f"   • {args.output_prefix}_polygons.geojson")
    print(f"   • {args.output_prefix}_centerlines.geojson")

if __name__=="__main__":
    main()
