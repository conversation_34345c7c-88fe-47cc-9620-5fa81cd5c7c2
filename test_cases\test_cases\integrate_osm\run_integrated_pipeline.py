#!/usr/bin/env python3
"""
Integrated LiDAR to GeoJSON Pipeline Runner
Complete pipeline from raw LiDAR to web-ready GeoJSON
"""

import sys
import time
import argparse
from pathlib import Path

# Import our integrated modules
from complete_lidar_pipeline import LiDARProcessor
from pdal_processor import PDALProcessor
from coordinate_transformer import transform_directory

def run_complete_pipeline(input_las: str, 
                         output_dir: str = "integrated_output",
                         use_pdal: bool = True,
                         chunk_size: float = 500.0,
                         intensity_min: int = 75,
                         intensity_max: int = 150,
                         input_crs: str = "EPSG:4326",
                         intermediate_crs: str = "EPSG:25832",
                         output_crs: str = "EPSG:4326",
                         create_web_version: bool = True):
    """
    Run the complete integrated pipeline
    
    Args:
        input_las (str): Path to input LAS file
        output_dir (str): Output directory
        use_pdal (bool): Whether to use PDAL for preprocessing
        chunk_size (float): Chunk size in meters
        intensity_min (int): Minimum intensity value
        intensity_max (int): Maximum intensity value
        input_crs (str): Input coordinate system
        intermediate_crs (str): Intermediate coordinate system for processing
        output_crs (str): Final output coordinate system
        create_web_version (bool): Create web-compatible version
    
    Returns:
        bool: True if successful
    """
    
    print("INTEGRATED LIDAR TO GEOJSON PIPELINE")
    print("=" * 60)
    print(f"Input: {input_las}")
    print(f"Output: {output_dir}")
    print(f"Chunk size: {chunk_size}m")
    print(f"Intensity range: {intensity_min}-{intensity_max}")
    print(f"Input CRS: {input_crs}")
    print(f"Processing CRS: {intermediate_crs}")
    print(f"Output CRS: {output_crs}")
    print("=" * 60)
    
    start_time = time.time()
    
    # Setup paths
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    input_las_path = Path(input_las)
    
    # Step 1: PDAL preprocessing (optional)
    if use_pdal:
        print("\nSTEP 1: PDAL Preprocessing")
        print("-" * 30)
        
        filtered_las = output_path / f"{input_las_path.stem}_filtered.las"
        
        pdal_processor = PDALProcessor(input_las, str(filtered_las))
        
        pipeline_config = pdal_processor.create_pipeline_config(
            intensity_min=intensity_min,
            intensity_max=intensity_max,
            input_crs=input_crs,
            output_crs=intermediate_crs
        )
        
        if not pdal_processor.run_pipeline(pipeline_config):
            print("PDAL preprocessing failed!")
            return False
        
        # Use filtered file for next step
        processing_las = str(filtered_las)
        
    else:
        print("\nSTEP 1: Skipping PDAL preprocessing")
        print("-" * 30)
        processing_las = input_las
    
    # Step 2: Feature extraction
    print("\nSTEP 2: Feature Extraction")
    print("-" * 30)
    
    # Create subdirectory for intermediate results
    intermediate_dir = output_path / "intermediate"
    
    processor = LiDARProcessor(
        processing_las, 
        chunk_size_meters=chunk_size, 
        output_dir=str(intermediate_dir)
    )
    
    if not processor.run_complete_pipeline(output_crs=intermediate_crs):
        print("Feature extraction failed!")
        return False
    
    # Step 3: Coordinate transformation to final CRS
    if intermediate_crs != output_crs:
        print(f"\nSTEP 3: Coordinate Transformation")
        print("-" * 30)
        
        final_dir = output_path / "final"
        
        # Determine source EPSG code
        source_epsg = int(intermediate_crs.split(':')[1])
        target_epsg = int(output_crs.split(':')[1])
        
        transform_directory(
            str(intermediate_dir),
            str(final_dir),
            source_epsg,
            target_epsg
        )
        
        final_output_dir = final_dir
    else:
        print("\nSTEP 3: Skipping coordinate transformation (same CRS)")
        print("-" * 30)
        final_output_dir = intermediate_dir
    
    # Step 4: Create web-compatible version (WGS84)
    if create_web_version and output_crs != "EPSG:4326":
        print(f"\nSTEP 4: Creating Web-Compatible Version (WGS84)")
        print("-" * 30)
        
        web_dir = output_path / "web_compatible"
        
        # Transform to WGS84
        source_epsg = int(output_crs.split(':')[1])
        target_epsg = 4326
        
        transform_directory(
            str(final_output_dir),
            str(web_dir),
            source_epsg,
            target_epsg
        )
        
        print(f"Web-compatible files created in: {web_dir}")
        print("These files are ready for geojson.io and web mapping!")
    
    elif output_crs == "EPSG:4326":
        print(f"\nSTEP 4: Output already in WGS84 format")
        print("-" * 30)
        print("Files are ready for geojson.io and web mapping!")
    
    else:
        print(f"\nSTEP 4: Skipping web-compatible version")
        print("-" * 30)
    
    # Summary
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n{'='*60}")
    print("PIPELINE COMPLETED SUCCESSFULLY!")
    print(f"{'='*60}")
    print(f"Total execution time: {duration:.2f} seconds")
    print(f"Output directory: {output_path}")
    print()
    print("Generated directories:")
    
    if use_pdal:
        print(f"  - Filtered LAS file: {output_path / f'{input_las_path.stem}_filtered.las'}")
    
    print(f"  - intermediate/ - Features in {intermediate_crs}")
    
    if intermediate_crs != output_crs:
        print(f"  - final/ - Features in {output_crs}")
    
    if create_web_version and output_crs != "EPSG:4326":
        print(f"  - web_compatible/ - Features in WGS84 for web use")
    
    print()
    print("Files ready for use:")
    print("  - polygons.geojson - Extracted polygon features")
    print("  - lines.geojson - Extracted line string features")
    print("  - points.geojson - Sample point features")
    print("  - combined_features.geojson - All features combined")
    
    return True

def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description='Integrated LiDAR to GeoJSON Pipeline',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic pipeline with PDAL preprocessing
  python run_integrated_pipeline.py -i input.las -o output/
  
  # Skip PDAL preprocessing (use pre-filtered file)
  python run_integrated_pipeline.py -i filtered.las -o output/ --no-pdal
  
  # Custom parameters
  python run_integrated_pipeline.py -i input.las -o output/ \\
    --chunk-size 1000 --intensity-min 50 --intensity-max 200 \\
    --output-crs EPSG:25834
  
  # Direct to WGS84 output
  python run_integrated_pipeline.py -i input.las -o output/ \\
    --intermediate-crs EPSG:25832 --output-crs EPSG:4326
        """
    )
    
    parser.add_argument('--input', '-i', required=True, 
                        help='Input LAS file path')
    parser.add_argument('--output', '-o', default='integrated_output',
                        help='Output directory (default: integrated_output)')
    parser.add_argument('--no-pdal', action='store_true',
                        help='Skip PDAL preprocessing')
    parser.add_argument('--chunk-size', type=float, default=500.0,
                        help='Chunk size in meters (default: 500)')
    parser.add_argument('--intensity-min', type=int, default=75,
                        help='Minimum intensity value (default: 75)')
    parser.add_argument('--intensity-max', type=int, default=150,
                        help='Maximum intensity value (default: 150)')
    parser.add_argument('--input-crs', default='EPSG:4326',
                        help='Input coordinate system (default: EPSG:4326)')
    parser.add_argument('--intermediate-crs', default='EPSG:25832',
                        help='Intermediate processing CRS (default: EPSG:25832)')
    parser.add_argument('--output-crs', default='EPSG:4326',
                        help='Final output CRS (default: EPSG:4326)')
    parser.add_argument('--no-web-version', action='store_true',
                        help='Skip creating web-compatible version')
    
    args = parser.parse_args()
    
    # Run the pipeline
    success = run_complete_pipeline(
        input_las=args.input,
        output_dir=args.output,
        use_pdal=not args.no_pdal,
        chunk_size=args.chunk_size,
        intensity_min=args.intensity_min,
        intensity_max=args.intensity_max,
        input_crs=args.input_crs,
        intermediate_crs=args.intermediate_crs,
        output_crs=args.output_crs,
        create_web_version=not args.no_web_version
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
