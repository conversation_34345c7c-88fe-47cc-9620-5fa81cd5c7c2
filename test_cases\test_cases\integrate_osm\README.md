# Integrated LiDAR to GeoJSON Pipeline

A complete, integrated solution for converting LiDAR data to GeoJSON format with support for multiple coordinate systems and web compatibility.

## Overview

This integrated pipeline combines all the functionality from the test_cases pipeline into a streamlined solution that:

1. **Preprocesses LiDAR data** using PDAL (optional)
2. **Processes data in chunks** for memory efficiency
3. **Extracts geometric features** using advanced algorithms
4. **Transforms coordinates** between different CRS
5. **Generates web-compatible output** for geojson.io and mapping tools

## Features

- **Modular Design**: Use individual components or the complete pipeline
- **Multiple CRS Support**: Input, processing, and output in different coordinate systems
- **Web Compatibility**: Automatic generation of WGS84 versions for web mapping
- **Scalable Processing**: Chunked processing for large LiDAR files
- **Advanced Feature Extraction**: RANSAC filtering, DBSCAN clustering, PCA centerlines
- **Flexible Configuration**: Command-line interface with extensive options

## Files

### Core Modules
- `complete_lidar_pipeline.py` - Main LiDAR processing and feature extraction
- `pdal_processor.py` - PDAL pipeline wrapper for preprocessing
- `coordinate_transformer.py` - Coordinate system transformation utilities
- `run_integrated_pipeline.py` - Complete integrated pipeline runner

### Configuration
- `requirements.txt` - Python package dependencies
- `README.md` - This documentation

## Installation

### 1. Install PDAL (Optional but Recommended)

```bash
# Using conda (recommended)
conda install -c conda-forge pdal

# Or visit https://pdal.io/en/latest/download.html
```

### 2. Install Python Dependencies

```bash
pip install -r requirements.txt
```

## Usage

### Quick Start - Complete Pipeline

```bash
# Basic usage with all defaults
python run_integrated_pipeline.py -i input.las -o output/

# Custom parameters
python run_integrated_pipeline.py -i input.las -o output/ \
  --chunk-size 1000 \
  --intensity-min 50 \
  --intensity-max 200 \
  --output-crs EPSG:25834
```

### Individual Components

#### 1. PDAL Preprocessing Only
```bash
python pdal_processor.py -i input.las -o filtered.las \
  --intensity-min 75 --intensity-max 150 \
  --input-crs EPSG:4326 --output-crs EPSG:25832
```

#### 2. Feature Extraction Only
```bash
python complete_lidar_pipeline.py -i filtered.las -o features/ \
  --chunk-size 500 --output-crs EPSG:4326
```

#### 3. Coordinate Transformation Only
```bash
# Single file
python coordinate_transformer.py -i input.geojson -o output.geojson \
  --source 25832 --target 4326

# Batch processing
python coordinate_transformer.py --batch -i input_dir/ -o output_dir/ \
  --source 25832 --target 4326
```

## Pipeline Workflow

### Default Pipeline (with PDAL)
```
Input LAS → PDAL Filter → Feature Extraction → Coordinate Transform → Web Version
(EPSG:4326)  (EPSG:25832)     (EPSG:25832)        (EPSG:4326)      (WGS84)
```

### Without PDAL
```
Filtered LAS → Feature Extraction → Coordinate Transform → Web Version
(EPSG:25832)     (EPSG:25832)        (EPSG:4326)      (WGS84)
```

## Output Structure

```
output_directory/
├── input_filtered.las              # PDAL filtered file (if used)
├── intermediate/                   # Features in processing CRS
│   ├── polygons.geojson
│   ├── lines.geojson
│   ├── points.geojson
│   └── combined_features.geojson
├── final/                         # Features in output CRS (if different)
│   ├── polygons.geojson
│   ├── lines.geojson
│   ├── points.geojson
│   └── combined_features.geojson
└── web_compatible/                # WGS84 version for web use
    ├── polygons.geojson
    ├── lines.geojson
    ├── points.geojson
    └── combined_features.geojson
```

## Configuration Options

### PDAL Preprocessing
- `--intensity-min/max`: Intensity filtering range
- `--input-crs`: Input coordinate system
- `--scale-x/y/z`: Scale factors for output
- `--offset-x/y/z`: Offset values for output

### Feature Extraction
- `--chunk-size`: Spatial chunk size in meters
- Processing uses RANSAC ground filtering and DBSCAN clustering
- Automatic polygon and line extraction with PCA centerlines

### Coordinate Systems
- `--input-crs`: Original LAS file CRS (default: EPSG:4326)
- `--intermediate-crs`: Processing CRS (default: EPSG:25832)
- `--output-crs`: Final output CRS (default: EPSG:4326)

## Coordinate System Support

### Commonly Used CRS
- **EPSG:4326** - WGS84 (lat/lon) - Web mapping standard
- **EPSG:25832** - ETRS89 / UTM zone 32N - Central Europe
- **EPSG:25834** - ETRS89 / UTM zone 34N - Eastern Europe
- **EPSG:3857** - Web Mercator - Web mapping projection

### Recommended Workflows

#### For Central Europe (Germany, Netherlands, etc.)
```bash
python run_integrated_pipeline.py -i input.las -o output/ \
  --input-crs EPSG:4326 \
  --intermediate-crs EPSG:25832 \
  --output-crs EPSG:4326
```

#### For Eastern Europe (Poland, Baltic states, etc.)
```bash
python run_integrated_pipeline.py -i input.las -o output/ \
  --input-crs EPSG:4326 \
  --intermediate-crs EPSG:25834 \
  --output-crs EPSG:4326
```

#### Direct to UTM (no web version)
```bash
python run_integrated_pipeline.py -i input.las -o output/ \
  --output-crs EPSG:25832 \
  --no-web-version
```

## Performance Tips

- **Chunk Size**: Larger chunks (1000m) for sparse data, smaller (250m) for dense data
- **Memory**: Reduce chunk size if running out of memory
- **Storage**: Use SSD for faster I/O with large files
- **Parallel Processing**: Process multiple files simultaneously

## Troubleshooting

### Common Issues

1. **PDAL not found**
   ```bash
   conda install -c conda-forge pdal
   ```

2. **Memory errors**
   - Reduce `--chunk-size` parameter
   - Process smaller areas at a time

3. **No features extracted**
   - Check intensity filtering range
   - Verify coordinate system settings
   - Ensure sufficient point density

4. **Coordinate transformation errors**
   - Verify EPSG codes are correct
   - Check that pyproj supports the CRS

### Performance Issues
- Use appropriate chunk sizes for your data density
- Ensure adequate RAM for your file size
- Use SSD storage for better I/O performance

## Integration with Other Tools

### Web Mapping
- Use files from `web_compatible/` directory
- Compatible with Leaflet, OpenLayers, Mapbox
- Direct upload to geojson.io

### Desktop GIS
- QGIS: Load any GeoJSON files directly
- ArcGIS: Import as feature classes
- Use appropriate CRS version for your region

### Databases
```bash
# Import to PostGIS
ogr2ogr -f "PostgreSQL" PG:"dbname=mydb" polygons.geojson

# Import to SQLite
ogr2ogr -f "SQLite" output.sqlite polygons.geojson
```

## License

This integrated pipeline is provided as-is for processing LiDAR data. Modify as needed for your specific requirements.

## Support

For issues or questions:
1. Check the troubleshooting section
2. Verify all dependencies are installed
3. Test with smaller datasets first
4. Check coordinate system compatibility
