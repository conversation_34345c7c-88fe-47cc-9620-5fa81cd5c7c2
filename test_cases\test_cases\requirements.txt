# LiDAR Processing Pipeline Requirements

# Core LiDAR processing
laspy>=2.0.0
numpy>=1.20.0
pandas>=1.3.0

# Geospatial processing
geopandas>=0.10.0
shapely>=1.8.0
pyproj>=3.0.0

# Machine learning for clustering and analysis
scikit-learn>=1.0.0

# Visualization
matplotlib>=3.5.0

# PDAL Python bindings (optional, CLI tool preferred)
# pdal>=3.0.0

# Note: PDAL command-line tool should be installed separately:
# conda install -c conda-forge pdal
# or visit: https://pdal.io/en/latest/download.html
