#!/usr/bin/env python3
"""
Model Line String Extractor
Extracts line strings from LiDAR data using clustering and PCA analysis
"""

import numpy as np
import pandas as pd
import laspy
import geopandas as gpd
from pathlib import Path
from sklearn.cluster import DBSCAN
from sklearn.decomposition import PCA
from shapely.geometry import LineString, Point
from scipy.spatial import ConvexHull
import json

class ModelLineExtractor:
    """Extract line strings from LiDAR point cloud using ML models"""
    
    def __init__(self, las_file, output_dir):
        self.las_file = Path(las_file)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.all_lines = []
        self.all_points = []
        
    def load_las_data(self):
        """Load LAS file and extract point data with intensity filtering"""
        print(f"[INFO] Loading LAS file: {self.las_file}")

        if not self.las_file.exists():
            raise FileNotFoundError(f"LAS file not found: {self.las_file}")

        # Read LAS file
        las = laspy.read(self.las_file)

        # Extract coordinates and attributes
        points = las.xyz

        print(f"[INFO] Total points in file: {len(points):,}")

        # Apply intensity filtering (20-45 range)
        if hasattr(las, 'intensity'):
            intensity = las.intensity
            print(f"[INFO] Original intensity range: {intensity.min()}-{intensity.max()}")

            # Filter points by intensity 20-45
            intensity_mask = (intensity >= 20) & (intensity <= 45)
            points = points[intensity_mask]

            print(f"[INFO] Points after intensity filtering (20-45): {len(points):,}")
            print(f"[INFO] Filtered out {np.sum(~intensity_mask):,} points")

            # Filter attributes as well
            attributes = {}
            attributes['intensity'] = intensity[intensity_mask]

            if hasattr(las, 'classification'):
                attributes['classification'] = las.classification[intensity_mask]
            if hasattr(las, 'return_number'):
                attributes['return_number'] = las.return_number[intensity_mask]
            if hasattr(las, 'number_of_returns'):
                attributes['number_of_returns'] = las.number_of_returns[intensity_mask]
        else:
            print("[WARNING] No intensity data found - proceeding without filtering")
            attributes = {}
            if hasattr(las, 'classification'):
                attributes['classification'] = las.classification
            if hasattr(las, 'return_number'):
                attributes['return_number'] = las.return_number
            if hasattr(las, 'number_of_returns'):
                attributes['number_of_returns'] = las.number_of_returns

        print(f"[INFO] Final point count for processing: {len(points):,}")
        print(f"[INFO] Available attributes: {list(attributes.keys())}")

        return points, attributes
    
    def create_spatial_chunks(self, points, chunk_size=100):
        """Divide points into spatial chunks for processing"""
        print(f"[INFO] Creating spatial chunks (size: {chunk_size}m)")
        
        # Get bounds
        min_x, min_y = points[:, 0].min(), points[:, 1].min()
        max_x, max_y = points[:, 0].max(), points[:, 1].max()
        
        print(f"[INFO] Data bounds: X({min_x:.2f}, {max_x:.2f}), Y({min_y:.2f}, {max_y:.2f})")
        
        # Calculate number of chunks
        x_chunks = int(np.ceil((max_x - min_x) / chunk_size))
        y_chunks = int(np.ceil((max_y - min_y) / chunk_size))
        
        print(f"[INFO] Creating {x_chunks} x {y_chunks} = {x_chunks * y_chunks} chunks")
        
        chunks = []
        chunk_id = 0
        
        for i in range(x_chunks):
            for j in range(y_chunks):
                # Define chunk bounds
                x_min = min_x + i * chunk_size
                x_max = min_x + (i + 1) * chunk_size
                y_min = min_y + j * chunk_size
                y_max = min_y + (j + 1) * chunk_size
                
                # Find points in this chunk
                mask = ((points[:, 0] >= x_min) & (points[:, 0] < x_max) & 
                       (points[:, 1] >= y_min) & (points[:, 1] < y_max))
                
                chunk_points = points[mask]
                
                if len(chunk_points) > 50:  # Only process chunks with sufficient points
                    chunks.append({
                        'id': chunk_id,
                        'bounds': (x_min, y_min, x_max, y_max),
                        'points': chunk_points,
                        'point_count': len(chunk_points)
                    })
                    chunk_id += 1
        
        print(f"[INFO] Created {len(chunks)} valid chunks")
        return chunks
    
    def extract_lines_from_chunk(self, chunk):
        """Extract line strings from a single chunk using clustering and PCA"""
        points = chunk['points']
        chunk_id = chunk['id']
        
        if len(points) < 10:
            return []
        
        # Use 2D points for clustering
        points_2d = points[:, :2]
        
        # DBSCAN clustering to find linear features
        eps = 2.0  # Maximum distance between points in a cluster
        min_samples = 10  # Minimum points to form a cluster
        
        clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(points_2d)
        labels = clustering.labels_
        
        unique_labels = set(labels)
        if -1 in unique_labels:
            unique_labels.remove(-1)  # Remove noise points
        
        lines = []
        
        for label in unique_labels:
            # Get points in this cluster
            cluster_mask = labels == label
            cluster_points = points_2d[cluster_mask]
            
            if len(cluster_points) < 10:
                continue
            
            # Check if cluster is linear (using PCA)
            pca = PCA(n_components=2).fit(cluster_points)
            explained_variance_ratio = pca.explained_variance_ratio_
            
            # If first component explains >80% of variance, it's likely linear
            if explained_variance_ratio[0] > 0.8:
                # Create convex hull for boundary
                try:
                    hull = ConvexHull(cluster_points)
                    hull_points = cluster_points[hull.vertices]
                    
                    # Create centerline using PCA
                    center = pca.mean_
                    direction = pca.components_[0]
                    
                    # Extend line along principal direction
                    span = max(
                        cluster_points[:, 0].max() - cluster_points[:, 0].min(),
                        cluster_points[:, 1].max() - cluster_points[:, 1].min()
                    ) * 1.2
                    
                    line_start = center - direction * span / 2
                    line_end = center + direction * span / 2
                    
                    line = LineString([line_start, line_end])
                    
                    # Calculate line properties
                    line_length = line.length
                    point_density = len(cluster_points) / line_length if line_length > 0 else 0
                    
                    # Store line with properties
                    line_data = {
                        'geometry': line,
                        'properties': {
                            'chunk_id': chunk_id,
                            'cluster_id': int(label),
                            'point_count': len(cluster_points),
                            'length': round(line_length, 2),
                            'point_density': round(point_density, 2),
                            'explained_variance': round(explained_variance_ratio[0], 3),
                            'source': 'model_extraction'
                        }
                    }
                    
                    lines.append(line_data)
                    
                except Exception as e:
                    print(f"[WARNING] Failed to create line for cluster {label} in chunk {chunk_id}: {e}")
                    continue
        
        return lines
    
    def process_chunks(self, chunks):
        """Process all chunks to extract line strings"""
        print(f"[INFO] Processing {len(chunks)} chunks for line extraction")
        
        total_lines = 0
        
        for i, chunk in enumerate(chunks):
            if i % 10 == 0:
                print(f"[INFO] Processing chunk {i+1}/{len(chunks)}")
            
            chunk_lines = self.extract_lines_from_chunk(chunk)
            self.all_lines.extend(chunk_lines)
            total_lines += len(chunk_lines)
        
        print(f"[INFO] Extracted {total_lines} line strings from {len(chunks)} chunks")
        return total_lines
    
    def save_model_lines(self):
        """Save extracted model lines to GeoJSON"""
        if not self.all_lines:
            print("[WARNING] No lines extracted")
            return
        
        # Create GeoDataFrame
        lines_data = []
        for line_item in self.all_lines:
            lines_data.append({
                **line_item['properties'],
                'geometry': line_item['geometry']
            })
        
        lines_gdf = gpd.GeoDataFrame(lines_data, crs="EPSG:25832")
        
        # Save to file
        output_file = self.output_dir / "model_lines_only.geojson"
        lines_gdf.to_file(output_file, driver="GeoJSON")
        
        print(f"[SUCCESS] Saved {len(lines_gdf)} model lines to: {output_file}")
        
        # Print statistics
        total_length = lines_gdf['length'].sum()
        avg_length = lines_gdf['length'].mean()
        print(f"[INFO] Total line length: {total_length:.2f} meters")
        print(f"[INFO] Average line length: {avg_length:.2f} meters")
        
        return output_file

def main():
    """Main execution function"""
    print("[INFO] Starting Model Line String Extraction")
    print("-" * 50)
    
    # Configuration
    las_file = "D:/Integrate/HT412_1738935654_3217135_1422974157258029_1422974185645127_Clip.las"
    output_dir = Path(__file__).parent
    
    # Initialize extractor
    extractor = ModelLineExtractor(las_file, output_dir)
    
    try:
        # Load LAS data
        points, attributes = extractor.load_las_data()
        
        # Create spatial chunks
        chunks = extractor.create_spatial_chunks(points, chunk_size=100)
        
        # Process chunks to extract lines
        total_lines = extractor.process_chunks(chunks)
        
        if total_lines > 0:
            # Save results
            output_file = extractor.save_model_lines()
            print(f"[SUCCESS] Model line extraction completed!")
        else:
            print("[WARNING] No lines were extracted from the data")
            
    except Exception as e:
        print(f"[ERROR] Model line extraction failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
