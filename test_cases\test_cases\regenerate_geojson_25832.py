#!/usr/bin/env python3
"""
Regenerate GeoJSON files in EPSG:25832 coordinate system
Converts existing GeoJSON files from EPSG:4326 to EPSG:25832 (UTM32N)
"""

import geopandas as gpd
from pathlib import Path
import os

def convert_geojson_to_25832():
    """Convert existing GeoJSON files to EPSG:25832"""
    
    script_dir = Path(__file__).parent
    input_dir = script_dir / "geojson_output"
    output_dir = script_dir / "geojson_output_25832"
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    print("Converting GeoJSON files to EPSG:25832")
    print("=" * 50)
    
    if not input_dir.exists():
        print(f"Input directory not found: {input_dir}")
        print("Please run the pipeline first!")
        return
    
    # Files to convert
    geojson_files = [
        'polygons.geojson',
        'lines.geojson', 
        'points.geojson'
    ]
    
    converted_files = []
    
    for filename in geojson_files:
        input_file = input_dir / filename
        output_file = output_dir / filename
        
        if input_file.exists():
            try:
                print(f"Converting {filename}...")
                
                # Read the GeoJSON file
                gdf = gpd.read_file(input_file)
                
                print(f"  Original CRS: {gdf.crs}")
                print(f"  Features: {len(gdf):,}")
                
                # Convert to EPSG:25832 (UTM32N)
                gdf_25832 = gdf.to_crs("EPSG:25832")
                
                # Save to new file
                gdf_25832.to_file(output_file, driver="GeoJSON")
                
                file_size = output_file.stat().st_size / 1024  # KB
                print(f"  Converted to: EPSG:25832")
                print(f"  Output file: {output_file}")
                print(f"  File size: {file_size:.1f} KB")
                print()
                
                converted_files.append(filename)
                
            except Exception as e:
                print(f"  Error converting {filename}: {str(e)}")
                print()
        else:
            print(f"File not found: {input_file}")
    
    # Create combined file in EPSG:25832
    if len(converted_files) >= 2:  # At least polygons and lines
        try:
            print("Creating combined features file...")
            
            combined_features = []
            
            # Add polygons
            polygons_file = output_dir / "polygons.geojson"
            if polygons_file.exists():
                polygons_gdf = gpd.read_file(polygons_file)
                for _, row in polygons_gdf.iterrows():
                    feature = {
                        "type": "Feature",
                        "geometry": row.geometry.__geo_interface__,
                        "properties": {**dict(row.drop('geometry')), "feature_type": "polygon"}
                    }
                    combined_features.append(feature)
            
            # Add lines
            lines_file = output_dir / "lines.geojson"
            if lines_file.exists():
                lines_gdf = gpd.read_file(lines_file)
                for _, row in lines_gdf.iterrows():
                    feature = {
                        "type": "Feature",
                        "geometry": row.geometry.__geo_interface__,
                        "properties": {**dict(row.drop('geometry')), "feature_type": "line"}
                    }
                    combined_features.append(feature)
            
            # Create combined GeoJSON
            combined_geojson = {
                "type": "FeatureCollection",
                "crs": {
                    "type": "name", 
                    "properties": {"name": "urn:ogc:def:crs:EPSG::25832"}
                },
                "features": combined_features
            }
            
            # Save combined file
            import json
            combined_file = output_dir / "combined_features.geojson"
            with open(combined_file, 'w') as f:
                json.dump(combined_geojson, f, indent=2)
            
            combined_size = combined_file.stat().st_size / 1024  # KB
            print(f"  Combined features: {len(combined_features):,}")
            print(f"  Output file: {combined_file}")
            print(f"  File size: {combined_size:.1f} KB")
            print()
            
        except Exception as e:
            print(f"Error creating combined file: {str(e)}")
    
    print("CONVERSION SUMMARY")
    print("=" * 50)
    print(f"Converted files: {len(converted_files)}")
    print(f"Output directory: {output_dir}")
    print(f"Target CRS: EPSG:25832 (ETRS89 / UTM zone 32N)")
    print()
    print("Files ready for use:")
    
    for filename in converted_files:
        output_file = output_dir / filename
        if output_file.exists():
            file_size = output_file.stat().st_size / 1024
            print(f"  - {filename} ({file_size:.1f} KB)")
    
    # Check if combined file exists
    combined_file = output_dir / "combined_features.geojson"
    if combined_file.exists():
        file_size = combined_file.stat().st_size / 1024
        print(f"  - combined_features.geojson ({file_size:.1f} KB)")
    
    print()
    print("USAGE NOTES:")
    print("- These files use EPSG:25832 (UTM Zone 32N) coordinates")
    print("- Should work better with geojson.io and other tools")
    print("- Coordinates are in meters (UTM projection)")
    print("- Matches your original PDAL pipeline CRS")
    print("- Can be loaded in QGIS, ArcGIS, or other GIS software")

def show_coordinate_info():
    """Show information about the coordinate systems"""
    print("\nCOORDINATE SYSTEM INFORMATION")
    print("=" * 50)
    print("EPSG:25832 - ETRS89 / UTM zone 32N")
    print("  - European Terrestrial Reference System 1989")
    print("  - UTM Zone 32N projection")
    print("  - Units: meters")
    print("  - Coverage: 6°E to 12°E, suitable for Central Europe")
    print("  - False Easting: 500,000 m")
    print("  - False Northing: 0 m")
    print("  - This matches your original PDAL pipeline configuration")
    print()
    print("This coordinate system should work well with:")
    print("  - geojson.io (better than EPSG:4326 for your data)")
    print("  - QGIS and other desktop GIS")
    print("  - Web mapping applications")
    print("  - PostGIS and spatial databases")
    print()
    print("Sample coordinates in EPSG:25832:")
    print("  - Your data bounds: X(691722 to 692355), Y(5360794 to 5364065)")
    print("  - These are UTM32N coordinates in meters")

def main():
    """Main function"""
    convert_geojson_to_25832()
    show_coordinate_info()

if __name__ == "__main__":
    main()
