# LiDAR Processing Pipeline

This pipeline processes LiDAR data to extract polygons and line strings, generating GeoJSON output. It follows the PDAL configuration provided and processes large files in chunks to handle memory constraints.

## Overview

The pipeline consists of four main components:

1. **PDAL Filtering and Reprojection** - Filters LiDAR data by intensity (75-150) and reprojects from EPSG:4326 to EPSG:25832 (UTM32N)
2. **Chunk Processing** - Divides large LiDAR files into spatial chunks for efficient processing
3. **Feature Extraction** - Extracts polygons and line strings using clustering and geometric analysis
4. **GeoJSON Generation** - Outputs features in GeoJSON format with proper CRS

## Files

- `pdal_pipeline.json` - PDAL configuration for filtering and reprojection
- `run_pdal_pipeline.py` - Executes PDAL pipeline
- `chunk_processor.py` - Processes LiDAR data in spatial chunks
- `geojson_generator.py` - Extracts features and generates GeoJSON
- `run_complete_pipeline.py` - Master script that runs the entire pipeline
- `requirements.txt` - Python package dependencies

## Installation

### 1. Install PDAL

PDAL is required for LiDAR data processing:

```bash
# Using conda (recommended)
conda install -c conda-forge pdal

# Or visit https://pdal.io/en/latest/download.html for other installation methods
```

### 2. Install Python Dependencies

```bash
pip install -r requirements.txt
```

## Usage

### Quick Start

Run the complete pipeline with a single command:

```bash
python run_complete_pipeline.py
```

This will:
1. Check dependencies
2. Verify input file exists
3. Run all pipeline steps in sequence
4. Generate output files

### Individual Steps

You can also run individual components:

```bash
# Step 1: PDAL filtering and reprojection
python run_pdal_pipeline.py

# Step 2: Process data in chunks
python chunk_processor.py

# Step 3: Generate GeoJSON features
python geojson_generator.py
```

## Input Requirements

- Input LAS file: `D:\review_sample\FusedLidar_Lidar1.las`
- The file should be in EPSG:4326 coordinate system
- Points should have intensity values

## Output

The pipeline generates several output files:

### Intermediate Files
- `FusedLidar_Lidar1_UTM32N_filtered.las` - Filtered and reprojected LAS file
- `chunks/` - Directory containing processed chunk files
  - `chunk_XXXX.npy` - Point cloud data for each chunk
  - `chunk_XXXX_metadata.json` - Metadata for each chunk
  - `processing_info.json` - Overall processing information

### Final GeoJSON Output
- `geojson_output/polygons.geojson` - Extracted polygon features
- `geojson_output/lines.geojson` - Extracted line string features  
- `geojson_output/points.geojson` - Sample point features
- `geojson_output/combined_features.geojson` - All features in one file

All GeoJSON files are in EPSG:4326 (WGS84) coordinate system.

## Processing Details

### PDAL Pipeline
- Reads input LAS file
- Filters points with intensity between 75-150
- Reprojects from EPSG:4326 to EPSG:25832 (UTM32N)
- Applies scale factors and offsets for precision

### Chunk Processing
- Divides data into 500m x 500m spatial chunks
- Processes each chunk independently
- Saves chunk data and metadata

### Feature Extraction
- Ground plane filtering using RANSAC
- DBSCAN clustering to identify feature groups
- Convex hull generation for polygons
- PCA-based centerline extraction for lines
- Adaptive parameters based on point density

## Configuration

### Chunk Size
Modify chunk size in `chunk_processor.py`:
```python
processor = LiDARChunkProcessor(str(las_file), chunk_size_meters=500.0)
```

### Clustering Parameters
Adjust clustering parameters in `geojson_generator.py`:
- DBSCAN eps (distance threshold)
- Minimum samples per cluster
- Ground plane threshold
- Minimum polygon area
- Minimum line length

### Output CRS
Change output coordinate system in `geojson_generator.py`:
```python
generator = GeoJSONGenerator(str(chunks_dir), output_crs="EPSG:4326")
```

## Troubleshooting

### Common Issues

1. **PDAL not found**
   - Install PDAL using conda: `conda install -c conda-forge pdal`
   - Ensure PDAL is in your system PATH

2. **Input file not found**
   - Verify the file path in `pdal_pipeline.json`
   - Ensure the file exists and is accessible

3. **Memory issues**
   - Reduce chunk size in `chunk_processor.py`
   - Process fewer chunks at a time

4. **No features extracted**
   - Check intensity filtering thresholds
   - Adjust clustering parameters
   - Verify coordinate system settings

### Performance Tips

- Use SSD storage for faster I/O
- Adjust chunk size based on available memory
- Process chunks in parallel for large datasets
- Use appropriate coordinate systems for your region

## Dependencies

- Python 3.7+
- PDAL (command-line tool)
- laspy - LAS file reading/writing
- numpy - Numerical computations
- pandas - Data manipulation
- geopandas - Geospatial data processing
- shapely - Geometric operations
- scikit-learn - Machine learning algorithms
- matplotlib - Visualization

## License

This pipeline is provided as-is for processing LiDAR data. Modify as needed for your specific requirements.
