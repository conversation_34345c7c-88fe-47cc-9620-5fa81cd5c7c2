#!/usr/bin/env python3
"""
LiDAR Chunk Processor
Processes large LiDAR files in chunks to handle memory constraints
"""

import laspy
import numpy as np
import os
import json
from pathlib import Path
import math
from typing import List, Tuple, Dict, Any

class LiDARChunkProcessor:
    """Process LiDAR data in spatial chunks"""
    
    def __init__(self, las_file: str, chunk_size_meters: float = 1000.0):
        """
        Initialize the chunk processor
        
        Args:
            las_file (str): Path to the LAS file
            chunk_size_meters (float): Size of each chunk in meters
        """
        self.las_file = las_file
        self.chunk_size = chunk_size_meters
        self.las_data = None
        self.bounds = None
        self.chunks_info = []
        
    def load_las_file(self) -> bool:
        """Load the LAS file and get basic information"""
        try:
            print(f"Loading LAS file: {self.las_file}")
            self.las_data = laspy.read(self.las_file)

            # Get bounds
            self.bounds = {
                'min_x': self.las_data.header.x_min,
                'max_x': self.las_data.header.x_max,
                'min_y': self.las_data.header.y_min,
                'max_y': self.las_data.header.y_max,
                'min_z': self.las_data.header.z_min,
                'max_z': self.las_data.header.z_max
            }

            print(f"LAS File Information:")
            print(f"   Points: {len(self.las_data.points):,}")
            print(f"   Bounds X: {self.bounds['min_x']:.2f} to {self.bounds['max_x']:.2f}")
            print(f"   Bounds Y: {self.bounds['min_y']:.2f} to {self.bounds['max_y']:.2f}")
            print(f"   Bounds Z: {self.bounds['min_z']:.2f} to {self.bounds['max_z']:.2f}")
            print(f"   CRS: {self.las_data.header.parse_crs()}")

            return True

        except Exception as e:
            print(f"Error loading LAS file: {str(e)}")
            return False
    
    def calculate_chunks(self) -> List[Dict[str, Any]]:
        """Calculate chunk boundaries"""
        if not self.bounds:
            raise ValueError("LAS file not loaded")
        
        # Calculate number of chunks in each direction
        x_range = self.bounds['max_x'] - self.bounds['min_x']
        y_range = self.bounds['max_y'] - self.bounds['min_y']
        
        x_chunks = math.ceil(x_range / self.chunk_size)
        y_chunks = math.ceil(y_range / self.chunk_size)
        
        print(f"Calculating chunks:")
        print(f"   X range: {x_range:.2f}m ({x_chunks} chunks)")
        print(f"   Y range: {y_range:.2f}m ({y_chunks} chunks)")
        print(f"   Total chunks: {x_chunks * y_chunks}")
        
        chunks = []
        chunk_id = 0
        
        for i in range(x_chunks):
            for j in range(y_chunks):
                x_min = self.bounds['min_x'] + i * self.chunk_size
                x_max = min(x_min + self.chunk_size, self.bounds['max_x'])
                y_min = self.bounds['min_y'] + j * self.chunk_size
                y_max = min(y_min + self.chunk_size, self.bounds['max_y'])
                
                chunk = {
                    'id': chunk_id,
                    'x_index': i,
                    'y_index': j,
                    'bounds': {
                        'min_x': x_min,
                        'max_x': x_max,
                        'min_y': y_min,
                        'max_y': y_max
                    }
                }
                chunks.append(chunk)
                chunk_id += 1
        
        self.chunks_info = chunks
        return chunks
    
    def extract_chunk_data(self, chunk: Dict[str, Any]) -> np.ndarray:
        """Extract point data for a specific chunk"""
        bounds = chunk['bounds']
        
        # Create mask for points within chunk bounds
        x_mask = (self.las_data.x >= bounds['min_x']) & (self.las_data.x < bounds['max_x'])
        y_mask = (self.las_data.y >= bounds['min_y']) & (self.las_data.y < bounds['max_y'])
        chunk_mask = x_mask & y_mask
        
        if not np.any(chunk_mask):
            return np.array([]).reshape(0, 7)  # Empty array with correct shape
        
        # Extract point attributes
        x = self.las_data.x[chunk_mask]
        y = self.las_data.y[chunk_mask]
        z = self.las_data.z[chunk_mask]
        intensity = self.las_data.intensity[chunk_mask]
        return_num = self.las_data.return_number[chunk_mask]
        num_returns = self.las_data.number_of_returns[chunk_mask]
        classification = self.las_data.classification[chunk_mask]
        
        # Stack into array
        points = np.column_stack([x, y, z, intensity, return_num, num_returns, classification])
        
        return points
    
    def process_chunks(self, output_dir: str = "chunks") -> List[str]:
        """Process all chunks and save to individual files"""
        if not self.chunks_info:
            self.calculate_chunks()
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        chunk_files = []
        
        print(f"Processing {len(self.chunks_info)} chunks...")

        for i, chunk in enumerate(self.chunks_info):
            print(f"   Processing chunk {i+1}/{len(self.chunks_info)} (ID: {chunk['id']})", end="")

            # Extract chunk data
            chunk_data = self.extract_chunk_data(chunk)

            if len(chunk_data) == 0:
                print(" - Empty chunk, skipping")
                continue

            # Save chunk data
            chunk_filename = f"chunk_{chunk['id']:04d}.npy"
            chunk_filepath = output_path / chunk_filename

            np.save(chunk_filepath, chunk_data)

            # Save chunk metadata
            metadata = {
                'chunk_id': chunk['id'],
                'bounds': chunk['bounds'],
                'point_count': len(chunk_data),
                'data_file': chunk_filename
            }

            metadata_filename = f"chunk_{chunk['id']:04d}_metadata.json"
            metadata_filepath = output_path / metadata_filename

            with open(metadata_filepath, 'w') as f:
                json.dump(metadata, f, indent=2)

            chunk_files.append(str(chunk_filepath))
            print(f" - {len(chunk_data):,} points saved")
        
        # Save overall processing info
        processing_info = {
            'source_file': self.las_file,
            'chunk_size_meters': self.chunk_size,
            'total_chunks': len(self.chunks_info),
            'processed_chunks': len(chunk_files),
            'bounds': self.bounds,
            'chunks': self.chunks_info
        }
        
        info_file = output_path / "processing_info.json"
        with open(info_file, 'w') as f:
            json.dump(processing_info, f, indent=2)
        
        print(f"Chunk processing complete!")
        print(f"   Processed: {len(chunk_files)} chunks")
        print(f"   Output directory: {output_path}")
        
        return chunk_files

def main():
    """Main function"""
    script_dir = Path(__file__).parent

    # Look for the filtered LAS file in parent directory first, then current directory
    las_file = script_dir.parent / "FusedLidar_Lidar1_UTM32N_filtered.las"

    if not las_file.exists():
        las_file = script_dir / "FusedLidar_Lidar1_UTM32N_filtered.las"

    if not las_file.exists():
        print(f"Filtered LAS file not found in:")
        print(f"   {script_dir.parent / 'FusedLidar_Lidar1_UTM32N_filtered.las'}")
        print(f"   {script_dir / 'FusedLidar_Lidar1_UTM32N_filtered.las'}")
        print("Please ensure the filtered LAS file exists!")
        return
    
    print("LiDAR Chunk Processor")
    print("=" * 50)
    
    # Initialize processor
    processor = LiDARChunkProcessor(str(las_file), chunk_size_meters=500.0)
    
    # Load file and process chunks
    if processor.load_las_file():
        chunk_files = processor.process_chunks(output_dir=str(script_dir / "chunks"))
        
        print(f"\nChunk processing completed!")
        print(f"Next step: Run geojson_generator.py to extract features")
    else:
        print("Failed to load LAS file!")

if __name__ == "__main__":
    main()
