#!/usr/bin/env python3
"""
Test Setup Script
Validates that all dependencies and files are properly configured
"""

import os
import sys
import subprocess
from pathlib import Path

def test_python_packages():
    """Test if required Python packages are available"""
    print("🔍 Testing Python packages...")
    
    packages = {
        'laspy': 'LAS file processing',
        'numpy': 'Numerical computations',
        'pandas': 'Data manipulation',
        'geopandas': 'Geospatial data processing',
        'shapely': 'Geometric operations',
        'sklearn': 'Machine learning',
        'matplotlib': 'Visualization'
    }
    
    all_good = True
    
    for package, description in packages.items():
        try:
            __import__(package)
            print(f"  ✅ {package} - {description}")
        except ImportError:
            print(f"  ❌ {package} - {description} (MISSING)")
            all_good = False
    
    return all_good

def test_pdal():
    """Test if PDAL command-line tool is available"""
    print("\n🔍 Testing PDAL...")
    
    try:
        result = subprocess.run(['pdal', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"  ✅ PDAL available - {version}")
            return True
        else:
            print(f"  ❌ PDAL command failed with return code {result.returncode}")
            return False
    except FileNotFoundError:
        print("  ❌ PDAL command not found")
        return False
    except subprocess.TimeoutExpired:
        print("  ❌ PDAL command timed out")
        return False
    except Exception as e:
        print(f"  ❌ Error testing PDAL: {str(e)}")
        return False

def test_input_file():
    """Test if input LAS file exists"""
    print("\n🔍 Testing input file...")
    
    input_file = Path("D:/review_sample/FusedLidar_Lidar1.las")
    
    if input_file.exists():
        file_size = input_file.stat().st_size / (1024 * 1024)  # MB
        print(f"  ✅ Input file found: {input_file}")
        print(f"     Size: {file_size:.2f} MB")
        return True
    else:
        print(f"  ❌ Input file not found: {input_file}")
        print("     Please ensure the file exists at the specified location")
        return False

def test_pipeline_files():
    """Test if all pipeline scripts exist"""
    print("\n🔍 Testing pipeline files...")
    
    script_dir = Path(__file__).parent
    
    required_files = [
        'pdal_pipeline.json',
        'run_pdal_pipeline.py',
        'chunk_processor.py', 
        'geojson_generator.py',
        'run_complete_pipeline.py',
        'requirements.txt',
        'README.md'
    ]
    
    all_good = True
    
    for filename in required_files:
        filepath = script_dir / filename
        if filepath.exists():
            print(f"  ✅ {filename}")
        else:
            print(f"  ❌ {filename} (MISSING)")
            all_good = False
    
    return all_good

def test_write_permissions():
    """Test if we can write to the output directories"""
    print("\n🔍 Testing write permissions...")
    
    script_dir = Path(__file__).parent
    
    test_dirs = [
        script_dir,
        script_dir / "chunks",
        script_dir / "geojson_output"
    ]
    
    all_good = True
    
    for test_dir in test_dirs:
        try:
            # Create directory if it doesn't exist
            test_dir.mkdir(exist_ok=True)
            
            # Try to write a test file
            test_file = test_dir / "test_write.tmp"
            test_file.write_text("test")
            test_file.unlink()  # Delete test file
            
            print(f"  ✅ {test_dir} - writable")
            
        except Exception as e:
            print(f"  ❌ {test_dir} - not writable ({str(e)})")
            all_good = False
    
    return all_good

def test_pdal_pipeline_syntax():
    """Test if PDAL pipeline JSON is valid"""
    print("\n🔍 Testing PDAL pipeline syntax...")
    
    script_dir = Path(__file__).parent
    pipeline_file = script_dir / "pdal_pipeline.json"
    
    if not pipeline_file.exists():
        print("  ❌ Pipeline file not found")
        return False
    
    try:
        import json
        with open(pipeline_file, 'r') as f:
            pipeline = json.load(f)
        
        # Basic validation
        if 'pipeline' not in pipeline:
            print("  ❌ Pipeline missing 'pipeline' key")
            return False
        
        if not isinstance(pipeline['pipeline'], list):
            print("  ❌ Pipeline 'pipeline' should be a list")
            return False
        
        if len(pipeline['pipeline']) < 2:
            print("  ❌ Pipeline should have at least 2 stages")
            return False
        
        print("  ✅ PDAL pipeline JSON syntax is valid")
        return True
        
    except json.JSONDecodeError as e:
        print(f"  ❌ Invalid JSON syntax: {str(e)}")
        return False
    except Exception as e:
        print(f"  ❌ Error validating pipeline: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🎯 LiDAR Pipeline Setup Test")
    print("=" * 50)
    
    tests = [
        ("Python Packages", test_python_packages),
        ("PDAL Tool", test_pdal),
        ("Input File", test_input_file),
        ("Pipeline Files", test_pipeline_files),
        ("Write Permissions", test_write_permissions),
        ("PDAL Pipeline Syntax", test_pdal_pipeline_syntax)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ Test failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Pipeline is ready to run.")
        print("Execute: python run_complete_pipeline.py")
    else:
        print(f"\n💥 {total - passed} test(s) failed. Please fix issues before running pipeline.")
        
        print("\nCommon fixes:")
        print("- Install missing packages: pip install -r requirements.txt")
        print("- Install PDAL: conda install -c conda-forge pdal")
        print("- Ensure input file exists at specified location")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
